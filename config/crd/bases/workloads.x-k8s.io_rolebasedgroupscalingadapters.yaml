---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.17.2
  name: rolebasedgroupscalingadapters.workloads.x-k8s.io
spec:
  group: workloads.x-k8s.io
  names:
    kind: RoleBasedGroupScalingAdapter
    listKind: RoleBasedGroupScalingAdapterList
    plural: rolebasedgroupscalingadapters
    shortNames:
    - rbgsa
    singular: rolebasedgroupscalingadapter
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .status.phase
      name: PHASE
      type: string
    - jsonPath: .status.replicas
      name: REPLICAS
      type: string
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: RoleBasedGroupScalingAdapter is the Schema for the rolebasedgroupscalingadapters
          API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
            type: string
          metadata:
            type: object
          spec:
            description: RoleBasedGroupScalingAdapterSpec defines the desired state
              of RoleBasedGroupScalingAdapter.
            properties:
              replicas:
                description: Replicas is the number of RoleBasedGroupRole that will
                  be scaled.
                format: int32
                type: integer
              scaleTargetRef:
                description: ScaleTargetRef is a reference to the target resource
                  that should be scaled.
                properties:
                  name:
                    type: string
                  role:
                    type: string
                required:
                - name
                - role
                type: object
            required:
            - scaleTargetRef
            type: object
          status:
            description: RoleBasedGroupScalingAdapterStatus shows the current state
              of a RoleBasedGroupScalingAdapter.
            properties:
              lastScaleTime:
                description: LastScaleTime is the last time the RoleBasedGroupScalingAdapter
                  scaled the number of pods,
                format: date-time
                type: string
              phase:
                description: Phase indicates the current phase of the RoleBasedGroupScalingAdapter.
                type: string
              replicas:
                description: Replicas is the current effective number of target RoleBasedGroupRole.
                format: int32
                type: integer
              selector:
                description: Selector is a label query used to filter and identify
                  a set of resources targeted for metrics collection.
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources:
      scale:
        labelSelectorPath: .status.selector
        specReplicasPath: .spec.replicas
        statusReplicasPath: .status.replicas
      status: {}
