apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    # for prometheus to scrape
    alibabacloud.com/inference-workload: vllm-inference
    alibabacloud.com/inference_backend: vllm
  name: vllm-inference
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      alibabacloud.com/inference-workload: vllm-inference
      alibabacloud.com/inference_backend: vllm
  template:
    metadata:
      labels:
        alibabacloud.com/inference-workload: vllm-inference
        alibabacloud.com/inference_backend: vllm
    spec:
      volumes:
        - name: model
          persistentVolumeClaim:
            claimName: llm-model
        - name: dshm
          emptyDir:
            medium: Memory
            sizeLimit: 15Gi
      containers:
        - command:
            - sh
            - -c
            - vllm serve /models/Qwen3-32B --port 8000 --trust-remote-code --max-model-len 2048 --gpu-memory-utilization 0.85 --tensor-parallel-size 2
          image: vllm/vllm-openai:v0.10.0
          name: vllm
          ports:
            - containerPort: 8000
              name: http
          readinessProbe:
            initialDelaySeconds: 30
            periodSeconds: 10
            tcpSocket:
              port: 8000
          resources:
            limits:
              nvidia.com/gpu: "2"
              memory: "16Gi"
              cpu: "4"
            requests:
              nvidia.com/gpu: "2"
              memory: "16Gi"
              cpu: "4"
          volumeMounts:
            - mountPath: /models/Qwen3-32B
              name: model
            - mountPath: /dev/shm
              name: dshm
---
apiVersion: v1
kind: Service
metadata:
  name: inference-service
spec:
  type: ClusterIP
  ports:
    - port: 8000
      protocol: TCP
      targetPort: 8000
  selector:
    alibabacloud.com/inference-workload: vllm-inference
