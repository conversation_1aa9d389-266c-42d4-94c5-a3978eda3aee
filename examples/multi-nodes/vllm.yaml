apiVersion: leaderworkerset.x-k8s.io/v1
kind: LeaderWorkerSet
metadata:
  name: vllm-multi-nodes
spec:
  replicas: 1
  leaderWorkerTemplate:
    size: 2
    restartPolicy: RecreateGroupOnPodRestart
    leaderTemplate:
      metadata:
        labels:
          role: leader
          # for prometheus to scrape
          alibabacloud.com/inference-workload: vllm-multi-nodes
          alibabacloud.com/inference_backend: vllm
      spec:
        volumes:
          - name: model
            persistentVolumeClaim:
              claimName: llm-model
          - name: dshm
            emptyDir:
              medium: Memory
              sizeLimit: 15Gi
        containers:
          - name: vllm-leader
            image: vllm/vllm-openai:v0.10.0
            command:
              - sh
              - -c
              - "bash /vllm-workspace/examples/online_serving/multi-node-serving.sh leader --ray_cluster_size=$(LWS_GROUP_SIZE); 
                 python3 -m vllm.entrypoints.openai.api_server --port 8000 --model /models/Qwen3-32B --tensor-parallel-size 2"
            resources:
              limits:
                nvidia.com/gpu: "1"
                memory: "48Gi"
                cpu: "12"
              requests:
                nvidia.com/gpu: "1"
                memory: "48Gi"
                cpu: "12"
            ports:
              - containerPort: 8000
                name: http
            readinessProbe:
              initialDelaySeconds: 30
              periodSeconds: 10
              tcpSocket:
                port: 8000
            volumeMounts:
              - mountPath: /models/Qwen3-32B
                name: model
              - mountPath: /dev/shm
                name: dshm
    workerTemplate:
      spec:
        volumes:
          - name: model
            persistentVolumeClaim:
              claimName: llm-model
          - name: dshm
            emptyDir:
              medium: Memory
              sizeLimit: 15Gi
        containers:
          - name: vllm-worker
            image: vllm/vllm-openai:v0.10.0
            command:
              - sh
              - -c
              - "bash /vllm-workspace/examples/online_serving/multi-node-serving.sh worker --ray_address=$(LWS_LEADER_ADDRESS)"
            resources:
              limits:
                nvidia.com/gpu: "1"
                memory: "48Gi"
                cpu: "12"
              requests:
                nvidia.com/gpu: "1"
                memory: "48Gi"
                cpu: "12"
            ports:
              - containerPort: 8000
            volumeMounts:
              - mountPath: /models/Qwen3-32B
                name: model
              - mountPath: /dev/shm
                name: dshm
---
apiVersion: v1
kind: Service
metadata:
  name: multi-nodes-service
spec:
  type: ClusterIP
  ports:
    - port: 8000
      protocol: TCP
      targetPort: 8000
  selector:
    alibabacloud.com/inference-workload: vllm-multi-nodes
    role: leader