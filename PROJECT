# Code generated by tool. DO NOT EDIT.
# This file is used to track the info used to scaffold your project
# and allow the plugins properly work.
# More info: https://book.kubebuilder.io/reference/project-config.html
domain: x-k8s.io
layout:
- go.kubebuilder.io/v4
multigroup: true
projectName: rbgs
repo: sigs.k8s.io/rbgs
resources:
- api:
    crdVersion: v1
    namespaced: true
  controller: true
  domain: x-k8s.io
  group: workloads
  kind: RoleBasedGroup
  path: sigs.k8s.io/rbgs/api/workloads/v1alpha1
  version: v1
- api:
    crdVersion: v1
    namespaced: true
  controller: true
  domain: x-k8s.io
  group: workloads
  kind: RoleBasedGroupSet
  path: sigs.k8s.io/rbgs/api/workloads/v1alpha1
  version: v1alpha1
- api:
    crdVersion: v1
  controller: true
  domain: x-k8s.io
  group: workloads
  kind: ClusterEngineRuntimeProfile
  path: sigs.k8s.io/rbgs/api/workloads/v1alpha1
  version: v1alpha1
version: "3"
