<!-- 
Please make sure you have read and understood the contributing guidelines;
https://github.com/sgl-project/rbg/blob/main/CONTRIBUTING.md -->

### Ⅰ. Motivation
<!-- Describe the purpose and goals of this pull request. -->

### Ⅱ. Modifications
<!-- Detail the changes made in this pull request. -->


### Ⅲ. Does this pull request fix one issue?
<!--If so, add "fixes #xxxx" so that the issue will be closed when this PR is merged (for example, "fixes #15" to close Issue #15). Otherwise, add "NONE" -->
fixes #XXXX

### Ⅳ. List the added test cases (unit test/integration test) if any, please explain if no tests are needed.


### Ⅴ. Describe how to verify it


### VI. Special notes for reviews


## Checklist

- [ ] Format your code `make fmt`.
- [ ] Add unit tests or integration tests.
- [ ] Update the documentation related to the change.