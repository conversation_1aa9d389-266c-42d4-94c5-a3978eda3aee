apiVersion: workloads.x-k8s.io/v1alpha1
kind: RoleBasedGroup
metadata:
  name: sglang-pd
spec:
  roles:
    - name: scheduler
      replicas: 1
      dependencies: [ "decode", "prefill" ]
      template:
        spec:
          volumes:
            - name: model
              persistentVolumeClaim:
                claimName: llm-model
          containers:
            - name: scheduler
              # lmsysorg/sglang:latest with mooncake
              image: anolis-registry.cn-zhangjiakou.cr.aliyuncs.com/anolis-docker-images/docker-temp:0.3.4.post2-sglang0.4.10.post2-pytorch2.7.1.8-cuda12.8.1-py312-alinux3.2104
              command:
                - sh
                - -c
                - python3 -m sglang.srt.disaggregation.mini_lb --prefill http://sglang-pd-prefill-0.sglang-pd-prefill:8000 http://sglang-pd-prefill-1.sglang-pd-prefill:8000 --prefill-bootstrap-ports 34000 34000 --decode http://sglang-pd-decode-0.sglang-pd-decode:8000 --host 0.0.0.0 --port 8000
              volumeMounts:
                - mountPath: /models/Qwen3-32B/
                  name: model
    - name: prefill
      replicas: 2
      template:
        metadata:
          labels:
            alibabacloud.com/inference-workload: sglang-pd-prefill
            alibabacloud.com/inference_backend: sglang
        spec:
          volumes:
            - name: model
              persistentVolumeClaim:
                claimName: llm-model
            - name: dshm
              emptyDir:
                medium: Memory
                sizeLimit: 15Gi
          containers:
            - name: sglang-prefill
              image: anolis-registry.cn-zhangjiakou.cr.aliyuncs.com/anolis-docker-images/docker-temp:0.3.4.post2-sglang0.4.10.post2-pytorch2.7.1.8-cuda12.8.1-py312-alinux3.2104
              env:
                - name: POD_IP
                  valueFrom:
                    fieldRef:
                      fieldPath: status.podIP
              command:
                - sh
                - -c
                - python3 -m sglang.launch_server --tp 2 --model-path /models/Qwen3-32B/ --disaggregation-mode prefill --port 8000 --disaggregation-bootstrap-port 34000 --host $(POD_IP) --enable-metrics
              ports:
                - containerPort: 8000
                  name: http
                - containerPort: 34000
                  name: bootstrap
              readinessProbe:
                initialDelaySeconds: 30
                periodSeconds: 10
                tcpSocket:
                  port: 8000
              resources:
                limits:
                  nvidia.com/gpu: "2"
                  rdma/hca: 1
                  memory: "48Gi"
                  cpu: "12"
                requests:
                  nvidia.com/gpu: "2"
                  rdma/hca: 1
                  memory: "48Gi"
                  cpu: "12"
              volumeMounts:
                - mountPath: /models/Qwen3-32B/
                  name: model
                - mountPath: /dev/shm
                  name: dshm

    - name: decode
      replicas: 1
      template:
        metadata:
          labels:
            alibabacloud.com/inference-workload: sglang-pd-decode
            alibabacloud.com/inference_backend: sglang
        spec:
          volumes:
            - name: model
              persistentVolumeClaim:
                claimName: llm-model
            - name: dshm
              emptyDir:
                medium: Memory
                sizeLimit: 15Gi
          containers:
            - name: sglang-decode
              image: anolis-registry.cn-zhangjiakou.cr.aliyuncs.com/anolis-docker-images/docker-temp:0.3.4.post2-sglang0.4.10.post2-pytorch2.7.1.8-cuda12.8.1-py312-alinux3.2104
              env:
                - name: POD_IP
                  valueFrom:
                    fieldRef:
                      fieldPath: status.podIP
              command:
                - sh
                - -c
                - python3 -m sglang.launch_server --tp 2 --model-path /models/Qwen3-32B/ --disaggregation-mode decode --port 8000 --host $(POD_IP) --enable-metrics
              ports:
                - containerPort: 8000
                  name: http
              readinessProbe:
                initialDelaySeconds: 30
                periodSeconds: 10
                tcpSocket:
                  port: 8000
              resources:
                limits:
                  nvidia.com/gpu: "2"
                  rdma/hca: 1
                  memory: "48Gi"
                  cpu: "12"
                requests:
                  nvidia.com/gpu: "2"
                  rdma/hca: 1
                  memory: "48Gi"
                  cpu: "12"
              volumeMounts:
                - mountPath: /models/Qwen3-32B/
                  name: model
                - mountPath: /dev/shm
                  name: dshm
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: sglang-pd
  name: sglang-pd
  namespace: default
spec:
  ports:
    - name: http
      port: 8000
      protocol: TCP
      targetPort: 8000
  selector:
    rolebasedgroup.workloads.x-k8s.io/name: sglang-pd
    rolebasedgroup.workloads.x-k8s.io/role: scheduler
  type: ClusterIP
