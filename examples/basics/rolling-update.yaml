apiVersion: workloads.x-k8s.io/v1alpha1
kind: RoleBasedGroup
metadata:
  name: rolling-update
spec:
  roles:
    - name: sts
      rolloutStrategy:
        rollingUpdate:
          maxUnavailable: 2
          maxSurge: 2
      replicas: 4
      template:
        metadata:
          labels:
            appVersion: v1
        spec:
          containers:
            - name: sts
              image: anolis-registry.cn-zhangjiakou.cr.aliyuncs.com/openanolis/nginx:1.14.1-8.6
              ports:
                - containerPort: 80

    - name: deployment
      workload:
        apiVersion: apps/v1
        kind: Deployment
      rolloutStrategy:
        rollingUpdate:
          maxUnavailable: 2
          maxSurge: 2
      replicas: 4
      template:
        metadata:
          labels:
            appVersion: v1
        spec:
          containers:
            - name: sts
              image: anolis-registry.cn-zhangjiakou.cr.aliyuncs.com/openanolis/nginx:1.14.1-8.6
              ports:
                - containerPort: 80

    - name: lws
      replicas: 4
      rolloutStrategy:
        rollingUpdate:
          maxUnavailable: 2
          maxSurge: 2
      workload:
        apiVersion: leaderworkerset.x-k8s.io/v1
        kind: LeaderWorkerSet
      leaderWorkerSet:
        size: 2
        patchLeaderTemplate:
          metadata:
            labels:
              role: leader
        patchWorkerTemplate:
          metadata:
            labels:
              role: worker
      template:
        metadata:
          labels:
            appVersion: v1
        spec:
          containers:
            - name: nginx
              image: anolis-registry.cn-zhangjiakou.cr.aliyuncs.com/openanolis/nginx:1.14.1-8.6
              ports:
                - name: http
                  containerPort: 8000