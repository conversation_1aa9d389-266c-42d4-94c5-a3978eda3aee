apiVersion: v1
kind: ServiceAccount
metadata:
  name: model-download-sa
  namespace: default
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: default
  name: model-download-role
rules:
  - apiGroups: [ "" ]
    resources: [ "secrets" ]
    resourceNames: [ "model-download-oss-secret" ]
    verbs: [ "get","patch" ]
  # The specific name of a Secret is not known during the authorization check for its creation.
  # Therefore, specifying resourceName in an access control policy will have no effect for 'create' operations.
  - apiGroups: [ "" ]
    resources: [ "secrets" ]
    verbs: [ "create" ]
  - apiGroups: [ "data.fluid.io" ]
    resources: [ "jindoruntimes","datasets" ]
    verbs: [ "create","get","patch" ]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  namespace: default
  name: model-download-rolebinding
subjects:
  - kind: ServiceAccount
    name: model-download-sa
    namespace: default
roleRef:
  kind: Role
  name: model-download-role
  apiGroup: rbac.authorization.k8s.io
---
apiVersion: batch/v1
kind: Job
metadata:
  name: model-download-job
spec:
  template:
    spec:
      serviceAccountName: model-download-sa
      containers:
        - name: model-download
          image: registry.cn-beijing.aliyuncs.com/zibai-test/model-download:v0.1.1
          imagePullPolicy: Always
          args:
            - --bucket-name
            - <your-bucket-name> # test-bucket
            - --bucket-endpoint
            - <your-oss-endpoint> # oss-ap-southeast-1-internal.aliyuncs.com
            # - --oss-url
            # - oss://<test-bucket>/Qwen2.5-7B-Instruct/
            - --git-url
            - https://www.modelscope.cn/Qwen/Qwen2.5-7B-Instruct.git
          env:
            - name: AK
              value: <your-oss-ak>
            - name: SK
              value: <your-oss-sk>
      restartPolicy: Never
