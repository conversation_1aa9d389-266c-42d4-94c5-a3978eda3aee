//go:build !ignore_autogenerated

/*
Copyright 2025.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by controller-gen. DO NOT EDIT.

package v1alpha1

import (
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
)

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *AdapterScaleTargetRef) DeepCopyInto(out *AdapterScaleTargetRef) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new AdapterScaleTargetRef.
func (in *AdapterScaleTargetRef) DeepCopy() *AdapterScaleTargetRef {
	if in == nil {
		return nil
	}
	out := new(AdapterScaleTargetRef)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ClusterEngineRuntimeProfile) DeepCopyInto(out *ClusterEngineRuntimeProfile) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	out.Status = in.Status
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ClusterEngineRuntimeProfile.
func (in *ClusterEngineRuntimeProfile) DeepCopy() *ClusterEngineRuntimeProfile {
	if in == nil {
		return nil
	}
	out := new(ClusterEngineRuntimeProfile)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *ClusterEngineRuntimeProfile) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ClusterEngineRuntimeProfileList) DeepCopyInto(out *ClusterEngineRuntimeProfileList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]ClusterEngineRuntimeProfile, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ClusterEngineRuntimeProfileList.
func (in *ClusterEngineRuntimeProfileList) DeepCopy() *ClusterEngineRuntimeProfileList {
	if in == nil {
		return nil
	}
	out := new(ClusterEngineRuntimeProfileList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *ClusterEngineRuntimeProfileList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ClusterEngineRuntimeProfileSpec) DeepCopyInto(out *ClusterEngineRuntimeProfileSpec) {
	*out = *in
	if in.InitContainers != nil {
		in, out := &in.InitContainers, &out.InitContainers
		*out = make([]v1.Container, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.Containers != nil {
		in, out := &in.Containers, &out.Containers
		*out = make([]v1.Container, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.Volumes != nil {
		in, out := &in.Volumes, &out.Volumes
		*out = make([]v1.Volume, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ClusterEngineRuntimeProfileSpec.
func (in *ClusterEngineRuntimeProfileSpec) DeepCopy() *ClusterEngineRuntimeProfileSpec {
	if in == nil {
		return nil
	}
	out := new(ClusterEngineRuntimeProfileSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ClusterEngineRuntimeProfileStatus) DeepCopyInto(out *ClusterEngineRuntimeProfileStatus) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ClusterEngineRuntimeProfileStatus.
func (in *ClusterEngineRuntimeProfileStatus) DeepCopy() *ClusterEngineRuntimeProfileStatus {
	if in == nil {
		return nil
	}
	out := new(ClusterEngineRuntimeProfileStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *EngineRuntime) DeepCopyInto(out *EngineRuntime) {
	*out = *in
	if in.InjectContainers != nil {
		in, out := &in.InjectContainers, &out.InjectContainers
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.Containers != nil {
		in, out := &in.Containers, &out.Containers
		*out = make([]v1.Container, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new EngineRuntime.
func (in *EngineRuntime) DeepCopy() *EngineRuntime {
	if in == nil {
		return nil
	}
	out := new(EngineRuntime)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KubeSchedulingPodGroupPolicySource) DeepCopyInto(out *KubeSchedulingPodGroupPolicySource) {
	*out = *in
	if in.ScheduleTimeoutSeconds != nil {
		in, out := &in.ScheduleTimeoutSeconds, &out.ScheduleTimeoutSeconds
		*out = new(int32)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KubeSchedulingPodGroupPolicySource.
func (in *KubeSchedulingPodGroupPolicySource) DeepCopy() *KubeSchedulingPodGroupPolicySource {
	if in == nil {
		return nil
	}
	out := new(KubeSchedulingPodGroupPolicySource)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *LeaderWorkerTemplate) DeepCopyInto(out *LeaderWorkerTemplate) {
	*out = *in
	if in.Size != nil {
		in, out := &in.Size, &out.Size
		*out = new(int32)
		**out = **in
	}
	in.PatchLeaderTemplate.DeepCopyInto(&out.PatchLeaderTemplate)
	in.PatchWorkerTemplate.DeepCopyInto(&out.PatchWorkerTemplate)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new LeaderWorkerTemplate.
func (in *LeaderWorkerTemplate) DeepCopy() *LeaderWorkerTemplate {
	if in == nil {
		return nil
	}
	out := new(LeaderWorkerTemplate)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *PodGroupPolicy) DeepCopyInto(out *PodGroupPolicy) {
	*out = *in
	in.PodGroupPolicySource.DeepCopyInto(&out.PodGroupPolicySource)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new PodGroupPolicy.
func (in *PodGroupPolicy) DeepCopy() *PodGroupPolicy {
	if in == nil {
		return nil
	}
	out := new(PodGroupPolicy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *PodGroupPolicySource) DeepCopyInto(out *PodGroupPolicySource) {
	*out = *in
	if in.KubeScheduling != nil {
		in, out := &in.KubeScheduling, &out.KubeScheduling
		*out = new(KubeSchedulingPodGroupPolicySource)
		(*in).DeepCopyInto(*out)
	}
	if in.VolcanoScheduling != nil {
		in, out := &in.VolcanoScheduling, &out.VolcanoScheduling
		*out = new(VolcanoSchedulingPodGroupPolicySource)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new PodGroupPolicySource.
func (in *PodGroupPolicySource) DeepCopy() *PodGroupPolicySource {
	if in == nil {
		return nil
	}
	out := new(PodGroupPolicySource)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RoleBasedGroup) DeepCopyInto(out *RoleBasedGroup) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RoleBasedGroup.
func (in *RoleBasedGroup) DeepCopy() *RoleBasedGroup {
	if in == nil {
		return nil
	}
	out := new(RoleBasedGroup)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *RoleBasedGroup) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RoleBasedGroupList) DeepCopyInto(out *RoleBasedGroupList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]RoleBasedGroup, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RoleBasedGroupList.
func (in *RoleBasedGroupList) DeepCopy() *RoleBasedGroupList {
	if in == nil {
		return nil
	}
	out := new(RoleBasedGroupList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *RoleBasedGroupList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RoleBasedGroupScalingAdapter) DeepCopyInto(out *RoleBasedGroupScalingAdapter) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RoleBasedGroupScalingAdapter.
func (in *RoleBasedGroupScalingAdapter) DeepCopy() *RoleBasedGroupScalingAdapter {
	if in == nil {
		return nil
	}
	out := new(RoleBasedGroupScalingAdapter)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *RoleBasedGroupScalingAdapter) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RoleBasedGroupScalingAdapterList) DeepCopyInto(out *RoleBasedGroupScalingAdapterList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]RoleBasedGroupScalingAdapter, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RoleBasedGroupScalingAdapterList.
func (in *RoleBasedGroupScalingAdapterList) DeepCopy() *RoleBasedGroupScalingAdapterList {
	if in == nil {
		return nil
	}
	out := new(RoleBasedGroupScalingAdapterList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *RoleBasedGroupScalingAdapterList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RoleBasedGroupScalingAdapterSpec) DeepCopyInto(out *RoleBasedGroupScalingAdapterSpec) {
	*out = *in
	if in.Replicas != nil {
		in, out := &in.Replicas, &out.Replicas
		*out = new(int32)
		**out = **in
	}
	if in.ScaleTargetRef != nil {
		in, out := &in.ScaleTargetRef, &out.ScaleTargetRef
		*out = new(AdapterScaleTargetRef)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RoleBasedGroupScalingAdapterSpec.
func (in *RoleBasedGroupScalingAdapterSpec) DeepCopy() *RoleBasedGroupScalingAdapterSpec {
	if in == nil {
		return nil
	}
	out := new(RoleBasedGroupScalingAdapterSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RoleBasedGroupScalingAdapterStatus) DeepCopyInto(out *RoleBasedGroupScalingAdapterStatus) {
	*out = *in
	if in.Replicas != nil {
		in, out := &in.Replicas, &out.Replicas
		*out = new(int32)
		**out = **in
	}
	if in.LastScaleTime != nil {
		in, out := &in.LastScaleTime, &out.LastScaleTime
		*out = (*in).DeepCopy()
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RoleBasedGroupScalingAdapterStatus.
func (in *RoleBasedGroupScalingAdapterStatus) DeepCopy() *RoleBasedGroupScalingAdapterStatus {
	if in == nil {
		return nil
	}
	out := new(RoleBasedGroupScalingAdapterStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RoleBasedGroupSet) DeepCopyInto(out *RoleBasedGroupSet) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RoleBasedGroupSet.
func (in *RoleBasedGroupSet) DeepCopy() *RoleBasedGroupSet {
	if in == nil {
		return nil
	}
	out := new(RoleBasedGroupSet)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *RoleBasedGroupSet) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RoleBasedGroupSetList) DeepCopyInto(out *RoleBasedGroupSetList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]RoleBasedGroupSet, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RoleBasedGroupSetList.
func (in *RoleBasedGroupSetList) DeepCopy() *RoleBasedGroupSetList {
	if in == nil {
		return nil
	}
	out := new(RoleBasedGroupSetList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *RoleBasedGroupSetList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RoleBasedGroupSetSpec) DeepCopyInto(out *RoleBasedGroupSetSpec) {
	*out = *in
	if in.Replicas != nil {
		in, out := &in.Replicas, &out.Replicas
		*out = new(int32)
		**out = **in
	}
	in.Template.DeepCopyInto(&out.Template)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RoleBasedGroupSetSpec.
func (in *RoleBasedGroupSetSpec) DeepCopy() *RoleBasedGroupSetSpec {
	if in == nil {
		return nil
	}
	out := new(RoleBasedGroupSetSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RoleBasedGroupSetStatus) DeepCopyInto(out *RoleBasedGroupSetStatus) {
	*out = *in
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]metav1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RoleBasedGroupSetStatus.
func (in *RoleBasedGroupSetStatus) DeepCopy() *RoleBasedGroupSetStatus {
	if in == nil {
		return nil
	}
	out := new(RoleBasedGroupSetStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RoleBasedGroupSpec) DeepCopyInto(out *RoleBasedGroupSpec) {
	*out = *in
	if in.Roles != nil {
		in, out := &in.Roles, &out.Roles
		*out = make([]RoleSpec, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.PodGroupPolicy != nil {
		in, out := &in.PodGroupPolicy, &out.PodGroupPolicy
		*out = new(PodGroupPolicy)
		(*in).DeepCopyInto(*out)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RoleBasedGroupSpec.
func (in *RoleBasedGroupSpec) DeepCopy() *RoleBasedGroupSpec {
	if in == nil {
		return nil
	}
	out := new(RoleBasedGroupSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RoleBasedGroupStatus) DeepCopyInto(out *RoleBasedGroupStatus) {
	*out = *in
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]metav1.Condition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.RoleStatuses != nil {
		in, out := &in.RoleStatuses, &out.RoleStatuses
		*out = make([]RoleStatus, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RoleBasedGroupStatus.
func (in *RoleBasedGroupStatus) DeepCopy() *RoleBasedGroupStatus {
	if in == nil {
		return nil
	}
	out := new(RoleBasedGroupStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RoleSpec) DeepCopyInto(out *RoleSpec) {
	*out = *in
	if in.Replicas != nil {
		in, out := &in.Replicas, &out.Replicas
		*out = new(int32)
		**out = **in
	}
	if in.RolloutStrategy != nil {
		in, out := &in.RolloutStrategy, &out.RolloutStrategy
		*out = new(RolloutStrategy)
		(*in).DeepCopyInto(*out)
	}
	if in.Dependencies != nil {
		in, out := &in.Dependencies, &out.Dependencies
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	out.Workload = in.Workload
	in.Template.DeepCopyInto(&out.Template)
	in.LeaderWorkerSet.DeepCopyInto(&out.LeaderWorkerSet)
	if in.ServicePorts != nil {
		in, out := &in.ServicePorts, &out.ServicePorts
		*out = make([]v1.ServicePort, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.EngineRuntimes != nil {
		in, out := &in.EngineRuntimes, &out.EngineRuntimes
		*out = make([]EngineRuntime, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.ScalingAdapter != nil {
		in, out := &in.ScalingAdapter, &out.ScalingAdapter
		*out = new(ScalingAdapter)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RoleSpec.
func (in *RoleSpec) DeepCopy() *RoleSpec {
	if in == nil {
		return nil
	}
	out := new(RoleSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RoleStatus) DeepCopyInto(out *RoleStatus) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RoleStatus.
func (in *RoleStatus) DeepCopy() *RoleStatus {
	if in == nil {
		return nil
	}
	out := new(RoleStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RollingUpdate) DeepCopyInto(out *RollingUpdate) {
	*out = *in
	out.MaxUnavailable = in.MaxUnavailable
	out.MaxSurge = in.MaxSurge
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RollingUpdate.
func (in *RollingUpdate) DeepCopy() *RollingUpdate {
	if in == nil {
		return nil
	}
	out := new(RollingUpdate)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RolloutStrategy) DeepCopyInto(out *RolloutStrategy) {
	*out = *in
	if in.RollingUpdate != nil {
		in, out := &in.RollingUpdate, &out.RollingUpdate
		*out = new(RollingUpdate)
		**out = **in
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RolloutStrategy.
func (in *RolloutStrategy) DeepCopy() *RolloutStrategy {
	if in == nil {
		return nil
	}
	out := new(RolloutStrategy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ScalingAdapter) DeepCopyInto(out *ScalingAdapter) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ScalingAdapter.
func (in *ScalingAdapter) DeepCopy() *ScalingAdapter {
	if in == nil {
		return nil
	}
	out := new(ScalingAdapter)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *VolcanoSchedulingPodGroupPolicySource) DeepCopyInto(out *VolcanoSchedulingPodGroupPolicySource) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new VolcanoSchedulingPodGroupPolicySource.
func (in *VolcanoSchedulingPodGroupPolicySource) DeepCopy() *VolcanoSchedulingPodGroupPolicySource {
	if in == nil {
		return nil
	}
	out := new(VolcanoSchedulingPodGroupPolicySource)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *WorkloadSpec) DeepCopyInto(out *WorkloadSpec) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new WorkloadSpec.
func (in *WorkloadSpec) DeepCopy() *WorkloadSpec {
	if in == nil {
		return nil
	}
	out := new(WorkloadSpec)
	in.DeepCopyInto(out)
	return out
}
