# Default values for rbgs-controller.

replicaCount: 2

image:
  repository: registry-cn-hangzhou.ack.aliyuncs.com/acs/rbgs-controller
  pullPolicy: IfNotPresent
  tag: v0.3.0

imagePullSecrets: []

podAnnotations: {}

podSecurityContext: {}
# fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
# runAsNonRoot: true
# runAsUser: 1000

resources:
  limits:
    cpu: 1000m
    memory: 1536Mi
  requests:
    cpu: 100m
    memory: 256Mi

nodeSelector: {}

tolerations: []

crdUpgrade:
  enabled: true
  # This sets the time-to-live (TTL) for crd-upgrade jobs. Default is 259200 seconds (3 days).
  ttlSecondsAfterFinished: 259200
  repository: registry-cn-hangzhou.ack.aliyuncs.com/dev/rbgs-upgrade-crd
  imageTag: v0.3.1-upgrade-crd


