apiVersion: workloads.x-k8s.io/v1alpha1
kind: RoleBasedGroup
metadata:
  name: nginx-cluster
spec:
  roles:
    - name: leader
      replicas: 1
      workload: { }
      template:
        spec:
          containers:
            - name: nginx-leader
              image: anolis-registry.cn-zhangjiakou.cr.aliyuncs.com/openanolis/nginx:1.14.1-8.6
              ports:
                - containerPort: 80

    - name: worker
      replicas: 3
      dependencies: [ "leader" ] # Specify the startup order of roles; worker roles are created only after the leader role is ready.
      workload:
        apiVersion: apps/v1
        kind: Deployment
      template:
        spec:
          containers:
            - name: nginx-worker
              image: anolis-registry.cn-zhangjiakou.cr.aliyuncs.com/openanolis/nginx:1.14.1-8.6
              ports:
                - containerPort: 8080

