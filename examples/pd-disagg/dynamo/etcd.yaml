apiVersion: v1
kind: Service
metadata:
  name: etcd
  labels:
    app: etcd
spec:
  ports:
    - port: 2379
      name: client
    - port: 2380
      name: peer
  clusterIP: None # Enables headless service mode
  selector:
    app: etcd
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: etcd
  labels:
    app: etcd
spec:
  selector:
    matchLabels:
      app: etcd
  serviceName: "etcd"
  replicas: 1
  template:
    metadata:
      labels:
        app: etcd
    spec:
      containers:
        - name: etcd
          # image: bitnami/etcd:3.5.19
          image: ac2-registry.cn-hangzhou.cr.aliyuncs.com/ac2/etcd:3.6.1
          volumeMounts:
            - name: data
              mountPath: /var/lib/etcd
          env:
            - name: ETCDCTL_API
              value: "3"
            - name: ALLOW_NONE_AUTHENTICATION
              value: "yes"
      volumes:
        - name: data
          emptyDir: {}