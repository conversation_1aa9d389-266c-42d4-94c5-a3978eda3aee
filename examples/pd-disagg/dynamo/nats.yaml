apiVersion: v1
kind: Service
metadata:
  name: nats
  labels:
    app: nats
spec:
  ports:
    - port: 4222
      name: client
    - port: 8222
      name: management
    - port: 6222
      name: cluster
  selector:
    app: nats
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nats
  labels:
    app: nats
spec:
  replicas: 1
  selector:
    matchLabels:
      app: nats
  template:
    metadata:
      labels:
        app: nats
    spec:
      containers:
        - name: nats
          # image: nats:latest
          image: ac2-registry.cn-hangzhou.cr.aliyuncs.com/ac2/nats:2.11.5
          args:
            - -js
            - --trace
            - -m
            - "8222"
          ports:
            - containerPort: 4222
            - containerPort: 8222
            - containerPort: 6222