apiVersion: workloads.x-k8s.io/v1alpha1
kind: RoleBasedGroup
metadata:
  name: gang-scheduling
spec:
  podGroupPolicy:
    # using Kubernetes scheduler-plugins for gang-scheduling
    kubeScheduling: {}
  roles:
    - name: role-sts
      replicas: 1
      template:
        spec:
          containers:
            - name: sts
              image: anolis-registry.cn-zhangjiakou.cr.aliyuncs.com/openanolis/nginx:1.14.1-8.6
              ports:
                - containerPort: 80
              resources:
                requests:
                  nvidia.com/gpu: "1"
                limits:
                  nvidia.com/gpu: "1"

    - name: role-deploy
      replicas: 1
      workload:
        apiVersion: apps/v1
        kind: Deployment
      template:
        spec:
          containers:
            - name: deploy
              image: anolis-registry.cn-zhangjiakou.cr.aliyuncs.com/openanolis/nginx:1.14.1-8.6
              ports:
                - containerPort: 80
              resources:
                requests:
                  nvidia.com/gpu: "1"
                limits:
                  nvidia.com/gpu: "1"



