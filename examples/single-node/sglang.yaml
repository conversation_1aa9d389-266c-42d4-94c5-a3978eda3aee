apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    # for prometheus to scrape
    alibabacloud.com/inference-workload: sgl-inference
    alibabacloud.com/inference_backend: sglang
  name: sgl-inference
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      alibabacloud.com/inference-workload: sgl-inference
      alibabacloud.com/inference_backend: sglang
  template:
    metadata:
      labels:
        alibabacloud.com/inference-workload: sgl-inference
        alibabacloud.com/inference_backend: sglang
    spec:
      volumes:
        - name: model
          persistentVolumeClaim:
            claimName: llm-model
        - name: dshm
          emptyDir:
            medium: Memory
            sizeLimit: 15Gi
      containers:
        - command:
            - sh
            - -c
            - "python3 -m sglang.launch_server --model-path /models/Qwen3-32B --tp 2 --trust-remote-code  --context-length 2048 --host 0.0.0.0 --port 8000 --enable-metrics"
          image: lmsysorg/sglang:latest
          name: sglang
          ports:
            - containerPort: 8000
              name: http
          readinessProbe:
            initialDelaySeconds: 30
            periodSeconds: 10
            tcpSocket:
              port: 8000
          resources:
            limits:
              nvidia.com/gpu: "2"
              memory: "16Gi"
              cpu: "4"
            requests:
              nvidia.com/gpu: "2"
              memory: "16Gi"
              cpu: "4"
          volumeMounts:
            - mountPath: /models/Qwen3-32B
              name: model
            - mountPath: /dev/shm
              name: dshm
---
apiVersion: v1
kind: Service
metadata:
  name: inference-service
spec:
  type: ClusterIP
  ports:
    - port: 8000
      protocol: TCP
      targetPort: 8000
  selector:
    alibabacloud.com/inference-workload: sgl-inference
