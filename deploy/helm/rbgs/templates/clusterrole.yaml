apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: rbgs-controller-role
rules:
  - apiGroups:
      - ""
    resources:
      - pods
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - workloads.x-k8s.io
    resources:
      - rolebasedgroupsets
      - clusterengineruntimeprofiles
    verbs:
      - get
      - list
      - watch
      - update
      - patch
  - apiGroups:
      - workloads.x-k8s.io
    resources:
      - rolebasedgroupscalingadapters
      - rolebasedgroups
    verbs:
      - get
      - list
      - watch
      - create
      - delete
      - update
      - patch
  - apiGroups:
      - apiextensions.k8s.io
    resources:
      - customresourcedefinitions
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - workloads.x-k8s.io
    resources:
      - rolebasedgroupsets/status
      - rolebasedgroups/status
      - rolebasedgroupscalingadapters/status
      - clusterengineruntimeprofiles/status
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
  - apiGroups:
      - "scheduling.x-k8s.io"
    resources:
      - podgroups
    verbs:
      - get
      - list
      - watch
      - create
      - update
      - patch
      - delete
  - apiGroups:
      - ""
    resources:
      - configmaps
    verbs:
      - get
      - list
      - watch
      - create
      - update
      - patch
      - delete
  - apiGroups:
      - leaderworkerset.x-k8s.io
    resources:
      - leaderworkersets
      - leaderworkersets/status
    verbs:
      - get
      - list
      - watch
      - create
      - update
      - patch
      - delete
  - apiGroups:
      - coordination.k8s.io
    resources:
      - leases
    verbs:
      - get
      - list
      - watch
      - create
      - update
      - patch
      - delete
  - apiGroups:
      - ""
    resources:
      - events
    verbs:
      - create
      - get
      - list
      - patch
      - update
      - watch
  - apiGroups:
      - ""
    resources:
      - services
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
  - apiGroups:
      - apps
    resources:
      - statefulsets
      - deployments
      - controllerrevisions
    verbs:
      - create
      - delete
      - get
      - list
      - patch
      - update
      - watch
  - apiGroups:
      - apps
    resources:
      - statefulsets/finalizers
      - deployments/finalizers
    verbs:
      - update
  - apiGroups:
      - apps
    resources:
      - statefulsets/status
      - deployments/status
    verbs:
      - get
      - patch
      - update