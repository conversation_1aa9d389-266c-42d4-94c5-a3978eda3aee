name: Project Check

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]

env:
  GO_VERSION: 1.24.1

# Declare default permissions as read only.
permissions: read-all

jobs:
  staticcheck:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5.0.0
      - uses: dominikh/staticcheck-action@v1.4.0

  lint:
    runs-on: ubuntu-latest
    env:
      GOPATH: ${{ github.workspace }}
      GO111MODULE: auto
    defaults:
      run:
        working-directory: ${{ env.GOPATH }}/src/sigs.k8s.io/rbgs
    steps:
      - name: Set up Go
        uses: actions/setup-go@v5.5.0
        with:
          go-version: ${{ env.GO_VERSION }}

      - name: Checkout code
        uses: actions/checkout@v5.0.0
        with:
          path: ${{ env.GOPATH }}/src/sigs.k8s.io/rbgs

      - name: Lint preparation
        run: |
          arch=$(go env GOARCH)
          os=$(go env GOOS)
          curl -L https://get.helm.sh/helm-v3.11.1-${os}-${arch}.tar.gz | tar -xz -C /tmp
          chmod u+x /tmp/${os}-${arch}/helm && sudo mv /tmp/${os}-${arch}/helm /usr/local/bin/helm

      - name: CRD validation check
        run: |
          make manifests
          git status --porcelain
          if [ -z "`git status --porcelain`" ]; then echo "CRD validation passed."; else echo "CRD validation failed. Please use 'make update-crd' to keep CRDs latest"; exit 1; fi      

      - name: Code check
        run: |
          make fmt vet lint
          find ./deploy/helm | grep Chart.yaml | xargs dirname | xargs helm lint

  build:
    env:
      GOPATH: ${{ github.workspace }}
      GO111MODULE: auto
      CI_TEST_FLAGS: -race -coverprofile=coverage.txt -covermode=atomic
    defaults:
      run:
        working-directory: ${{ env.GOPATH }}/src/sigs.k8s.io/rbgs
    runs-on: ubuntu-latest
    steps:
      - name: Set up Go
        uses: actions/setup-go@v5.5.0
        with:
          go-version: ${{ env.GO_VERSION }}

      - name: Checkout code
        uses: actions/checkout@v5.0.0
        with:
          path: ${{ env.GOPATH }}/src/sigs.k8s.io/rbgs

      - name: CI Preparation
        run: |
          arch=$(go env GOARCH)
          os=$(go env GOOS)
          curl -L https://github.com/kubernetes-sigs/kubebuilder/releases/download/v2.3.1/kubebuilder_2.3.1_${os}_${arch}.tar.gz | tar -xz -C /tmp/
          sudo mv /tmp/kubebuilder_2.3.1_${os}_${arch} /usr/local/kubebuilder
          export PATH=$PATH:/usr/local/kubebuilder/bin

      - name: CI script
        run: |
          make build
          make test

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v5
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
