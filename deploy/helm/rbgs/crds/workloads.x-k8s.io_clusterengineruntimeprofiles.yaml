---
apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.17.2
  name: clusterengineruntimeprofiles.workloads.x-k8s.io
spec:
  group: workloads.x-k8s.io
  names:
    kind: ClusterEngineRuntimeProfile
    listKind: ClusterEngineRuntimeProfileList
    plural: clusterengineruntimeprofiles
    singular: clusterengineruntimeprofile
  scope: Cluster
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: ClusterEngineRuntimeProfile is the Schema for the clusterengineruntimeprofiles
          API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
            type: string
          metadata:
            type: object
          spec:
            description: ClusterEngineRuntimeProfileSpec defines the desired state
              of ClusterEngineRuntimeProfile.
            properties:
              containers:
                items:
                  description: A single application container that you want to run
                    within a pod.
                  properties:
                    args:
                      description: |-
                        Arguments to the entrypoint.
                        The container image's CMD is used if this is not provided.
                        Variable references $(VAR_NAME) are expanded using the container's environment.
                      items:
                        type: string
                      type: array
                      x-kubernetes-list-type: atomic
                    command:
                      description: |-
                        Entrypoint array. Not executed within a shell.
                        The container image's ENTRYPOINT is used if this is not provided.
                        Variable references $(VAR_NAME) are expanded using the container's environment.
                      items:
                        type: string
                      type: array
                      x-kubernetes-list-type: atomic
                    env:
                      description: |-
                        List of environment variables to set in the container.
                        Cannot be updated.
                      items:
                        description: EnvVar represents an environment variable present
                          in a Container.
                        properties:
                          name:
                            description: Name of the environment variable. Must be
                              a C_IDENTIFIER.
                            type: string
                          value:
                            description: |-
                              Variable references $(VAR_NAME) are expanded
                              using the previously defined environment variables in the container and
                              any service environment variables.
                            type: string
                          valueFrom:
                            description: Source for the environment variable's value.
                              Cannot be used if value is not empty.
                            properties:
                              configMapKeyRef:
                                description: Selects a key of a ConfigMap.
                                properties:
                                  key:
                                    description: The key to select.
                                    type: string
                                  name:
                                    default: ""
                                    description: |-
                                      Name of the referent.
                                      This field is effectively required, but due to backwards compatibility is
                                      allowed to be empty. Instances of this type with an empty value here are
                                      almost certainly wrong.
                                    type: string
                                  optional:
                                    description: Specify whether the ConfigMap or
                                      its key must be defined
                                    type: boolean
                                required:
                                - key
                                type: object
                                x-kubernetes-map-type: atomic
                              fieldRef:
                                description: |-
                                  Selects a field of the pod: supports metadata.name, metadata.namespace, `metadata.labels['<KEY>']`, `metadata.annotations['<KEY>']`,
                                  spec.nodeName, spec.serviceAccountName, status.hostIP, status.
                                properties:
                                  apiVersion:
                                    description: Version of the schema the FieldPath
                                      is written in terms of, defaults to "v1".
                                    type: string
                                  fieldPath:
                                    description: Path of the field to select in the
                                      specified API version.
                                    type: string
                                required:
                                - fieldPath
                                type: object
                                x-kubernetes-map-type: atomic
                              resourceFieldRef:
                                description: |-
                                  Selects a resource of the container: only resources limits and requests
                                  (limits.cpu, limits.memory, limits.ephemeral-storage, requests.cpu, requests.memory and requests.
                                properties:
                                  containerName:
                                    description: 'Container name: required for volumes,
                                      optional for env vars'
                                    type: string
                                  divisor:
                                    anyOf:
                                    - type: integer
                                    - type: string
                                    description: Specifies the output format of the
                                      exposed resources, defaults to "1"
                                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                    x-kubernetes-int-or-string: true
                                  resource:
                                    description: 'Required: resource to select'
                                    type: string
                                required:
                                - resource
                                type: object
                                x-kubernetes-map-type: atomic
                              secretKeyRef:
                                description: Selects a key of a secret in the pod's
                                  namespace
                                properties:
                                  key:
                                    description: The key of the secret to select from.  Must
                                      be a valid secret key.
                                    type: string
                                  name:
                                    default: ""
                                    description: |-
                                      Name of the referent.
                                      This field is effectively required, but due to backwards compatibility is
                                      allowed to be empty. Instances of this type with an empty value here are
                                      almost certainly wrong.
                                    type: string
                                  optional:
                                    description: Specify whether the Secret or its
                                      key must be defined
                                    type: boolean
                                required:
                                - key
                                type: object
                                x-kubernetes-map-type: atomic
                            type: object
                        required:
                        - name
                        type: object
                      type: array
                      x-kubernetes-list-map-keys:
                      - name
                      x-kubernetes-list-type: map
                    envFrom:
                      description: |-
                        List of sources to populate environment variables in the container.
                        The keys defined within a source must be a C_IDENTIFIER.
                      items:
                        description: EnvFromSource represents the source of a set
                          of ConfigMaps or Secrets
                        properties:
                          configMapRef:
                            description: The ConfigMap to select from
                            properties:
                              name:
                                default: ""
                                description: |-
                                  Name of the referent.
                                  This field is effectively required, but due to backwards compatibility is
                                  allowed to be empty. Instances of this type with an empty value here are
                                  almost certainly wrong.
                                type: string
                              optional:
                                description: Specify whether the ConfigMap must be
                                  defined
                                type: boolean
                            type: object
                            x-kubernetes-map-type: atomic
                          prefix:
                            description: Optional text to prepend to the name of each
                              environment variable. Must be a C_IDENTIFIER.
                            type: string
                          secretRef:
                            description: The Secret to select from
                            properties:
                              name:
                                default: ""
                                description: |-
                                  Name of the referent.
                                  This field is effectively required, but due to backwards compatibility is
                                  allowed to be empty. Instances of this type with an empty value here are
                                  almost certainly wrong.
                                type: string
                              optional:
                                description: Specify whether the Secret must be defined
                                type: boolean
                            type: object
                            x-kubernetes-map-type: atomic
                        type: object
                      type: array
                      x-kubernetes-list-type: atomic
                    image:
                      description: |-
                        Container image name.
                        More info: https://kubernetes.
                      type: string
                    imagePullPolicy:
                      description: |-
                        Image pull policy.
                        One of Always, Never, IfNotPresent.
                        Defaults to Always if :latest tag is specified, or IfNotPresent otherwise.
                        Cannot be updated.
                        More info: https://kubernetes.
                      type: string
                    lifecycle:
                      description: |-
                        Actions that the management system should take in response to container lifecycle events.
                        Cannot be updated.
                      properties:
                        postStart:
                          description: |-
                            PostStart is called immediately after a container is created. If the handler fails,
                            the container is terminated and restarted according to its restart policy.
                          properties:
                            exec:
                              description: Exec specifies a command to execute in
                                the container.
                              properties:
                                command:
                                  description: |-
                                    Command is the command line to execute inside the container, the working directory for the
                                    command  is root ('/') in the container's filesystem.
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                              type: object
                            httpGet:
                              description: HTTPGet specifies an HTTP GET request to
                                perform.
                              properties:
                                host:
                                  description: |-
                                    Host name to connect to, defaults to the pod IP. You probably want to set
                                    "Host" in httpHeaders instead.
                                  type: string
                                httpHeaders:
                                  description: Custom headers to set in the request.
                                    HTTP allows repeated headers.
                                  items:
                                    description: HTTPHeader describes a custom header
                                      to be used in HTTP probes
                                    properties:
                                      name:
                                        description: |-
                                          The header field name.
                                          This will be canonicalized upon output, so case-variant names will be understood as the same header.
                                        type: string
                                      value:
                                        description: The header field value
                                        type: string
                                    required:
                                    - name
                                    - value
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                path:
                                  description: Path to access on the HTTP server.
                                  type: string
                                port:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  description: |-
                                    Name or number of the port to access on the container.
                                    Number must be in the range 1 to 65535.
                                    Name must be an IANA_SVC_NAME.
                                  x-kubernetes-int-or-string: true
                                scheme:
                                  description: |-
                                    Scheme to use for connecting to the host.
                                    Defaults to HTTP.
                                  type: string
                              required:
                              - port
                              type: object
                            sleep:
                              description: Sleep represents a duration that the container
                                should sleep.
                              properties:
                                seconds:
                                  description: Seconds is the number of seconds to
                                    sleep.
                                  format: int64
                                  type: integer
                              required:
                              - seconds
                              type: object
                            tcpSocket:
                              description: |-
                                Deprecated. TCPSocket is NOT supported as a LifecycleHandler and kept
                                for backward compatibility. There is no validation of this field and
                                lifecycle hooks will fail at runtime when it is specified.
                              properties:
                                host:
                                  description: 'Optional: Host name to connect to,
                                    defaults to the pod IP.'
                                  type: string
                                port:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  description: |-
                                    Number or name of the port to access on the container.
                                    Number must be in the range 1 to 65535.
                                    Name must be an IANA_SVC_NAME.
                                  x-kubernetes-int-or-string: true
                              required:
                              - port
                              type: object
                          type: object
                        preStop:
                          description: |-
                            PreStop is called immediately before a container is terminated due to an
                            API request or management event such as liveness/startup probe failure,
                            preemption, resource contention, etc.
                          properties:
                            exec:
                              description: Exec specifies a command to execute in
                                the container.
                              properties:
                                command:
                                  description: |-
                                    Command is the command line to execute inside the container, the working directory for the
                                    command  is root ('/') in the container's filesystem.
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                              type: object
                            httpGet:
                              description: HTTPGet specifies an HTTP GET request to
                                perform.
                              properties:
                                host:
                                  description: |-
                                    Host name to connect to, defaults to the pod IP. You probably want to set
                                    "Host" in httpHeaders instead.
                                  type: string
                                httpHeaders:
                                  description: Custom headers to set in the request.
                                    HTTP allows repeated headers.
                                  items:
                                    description: HTTPHeader describes a custom header
                                      to be used in HTTP probes
                                    properties:
                                      name:
                                        description: |-
                                          The header field name.
                                          This will be canonicalized upon output, so case-variant names will be understood as the same header.
                                        type: string
                                      value:
                                        description: The header field value
                                        type: string
                                    required:
                                    - name
                                    - value
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                path:
                                  description: Path to access on the HTTP server.
                                  type: string
                                port:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  description: |-
                                    Name or number of the port to access on the container.
                                    Number must be in the range 1 to 65535.
                                    Name must be an IANA_SVC_NAME.
                                  x-kubernetes-int-or-string: true
                                scheme:
                                  description: |-
                                    Scheme to use for connecting to the host.
                                    Defaults to HTTP.
                                  type: string
                              required:
                              - port
                              type: object
                            sleep:
                              description: Sleep represents a duration that the container
                                should sleep.
                              properties:
                                seconds:
                                  description: Seconds is the number of seconds to
                                    sleep.
                                  format: int64
                                  type: integer
                              required:
                              - seconds
                              type: object
                            tcpSocket:
                              description: |-
                                Deprecated. TCPSocket is NOT supported as a LifecycleHandler and kept
                                for backward compatibility. There is no validation of this field and
                                lifecycle hooks will fail at runtime when it is specified.
                              properties:
                                host:
                                  description: 'Optional: Host name to connect to,
                                    defaults to the pod IP.'
                                  type: string
                                port:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  description: |-
                                    Number or name of the port to access on the container.
                                    Number must be in the range 1 to 65535.
                                    Name must be an IANA_SVC_NAME.
                                  x-kubernetes-int-or-string: true
                              required:
                              - port
                              type: object
                          type: object
                        stopSignal:
                          description: |-
                            StopSignal defines which signal will be sent to a container when it is being stopped.
                            If not specified, the default is defined by the container runtime in use.
                          type: string
                      type: object
                    livenessProbe:
                      description: |-
                        Periodic probe of container liveness.
                        Container will be restarted if the probe fails.
                        Cannot be updated.
                        More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes
                      properties:
                        exec:
                          description: Exec specifies a command to execute in the
                            container.
                          properties:
                            command:
                              description: |-
                                Command is the command line to execute inside the container, the working directory for the
                                command  is root ('/') in the container's filesystem.
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                          type: object
                        failureThreshold:
                          description: |-
                            Minimum consecutive failures for the probe to be considered failed after having succeeded.
                            Defaults to 3. Minimum value is 1.
                          format: int32
                          type: integer
                        grpc:
                          description: GRPC specifies a GRPC HealthCheckRequest.
                          properties:
                            port:
                              description: Port number of the gRPC service. Number
                                must be in the range 1 to 65535.
                              format: int32
                              type: integer
                            service:
                              default: ""
                              description: |-
                                Service is the name of the service to place in the gRPC HealthCheckRequest
                                (see https://github.com/grpc/grpc/blob/master/doc/health-checking.md).
                              type: string
                          required:
                          - port
                          type: object
                        httpGet:
                          description: HTTPGet specifies an HTTP GET request to perform.
                          properties:
                            host:
                              description: |-
                                Host name to connect to, defaults to the pod IP. You probably want to set
                                "Host" in httpHeaders instead.
                              type: string
                            httpHeaders:
                              description: Custom headers to set in the request. HTTP
                                allows repeated headers.
                              items:
                                description: HTTPHeader describes a custom header
                                  to be used in HTTP probes
                                properties:
                                  name:
                                    description: |-
                                      The header field name.
                                      This will be canonicalized upon output, so case-variant names will be understood as the same header.
                                    type: string
                                  value:
                                    description: The header field value
                                    type: string
                                required:
                                - name
                                - value
                                type: object
                              type: array
                              x-kubernetes-list-type: atomic
                            path:
                              description: Path to access on the HTTP server.
                              type: string
                            port:
                              anyOf:
                              - type: integer
                              - type: string
                              description: |-
                                Name or number of the port to access on the container.
                                Number must be in the range 1 to 65535.
                                Name must be an IANA_SVC_NAME.
                              x-kubernetes-int-or-string: true
                            scheme:
                              description: |-
                                Scheme to use for connecting to the host.
                                Defaults to HTTP.
                              type: string
                          required:
                          - port
                          type: object
                        initialDelaySeconds:
                          description: |-
                            Number of seconds after the container has started before liveness probes are initiated.
                            More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes
                          format: int32
                          type: integer
                        periodSeconds:
                          description: |-
                            How often (in seconds) to perform the probe.
                            Default to 10 seconds. Minimum value is 1.
                          format: int32
                          type: integer
                        successThreshold:
                          description: |-
                            Minimum consecutive successes for the probe to be considered successful after having failed.
                            Defaults to 1. Must be 1 for liveness and startup. Minimum value is 1.
                          format: int32
                          type: integer
                        tcpSocket:
                          description: TCPSocket specifies a connection to a TCP port.
                          properties:
                            host:
                              description: 'Optional: Host name to connect to, defaults
                                to the pod IP.'
                              type: string
                            port:
                              anyOf:
                              - type: integer
                              - type: string
                              description: |-
                                Number or name of the port to access on the container.
                                Number must be in the range 1 to 65535.
                                Name must be an IANA_SVC_NAME.
                              x-kubernetes-int-or-string: true
                          required:
                          - port
                          type: object
                        terminationGracePeriodSeconds:
                          description: Optional duration in seconds the pod needs
                            to terminate gracefully upon probe failure.
                          format: int64
                          type: integer
                        timeoutSeconds:
                          description: |-
                            Number of seconds after which the probe times out.
                            Defaults to 1 second. Minimum value is 1.
                            More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes
                          format: int32
                          type: integer
                      type: object
                    name:
                      description: |-
                        Name of the container specified as a DNS_LABEL.
                        Each container in a pod must have a unique name (DNS_LABEL).
                        Cannot be updated.
                      type: string
                    ports:
                      description: |-
                        List of ports to expose from the container. Not specifying a port here
                        DOES NOT prevent that port from being exposed. Any port which is
                        listening on the default "0.0.0.
                      items:
                        description: ContainerPort represents a network port in a
                          single container.
                        properties:
                          containerPort:
                            description: |-
                              Number of port to expose on the pod's IP address.
                              This must be a valid port number, 0 < x < 65536.
                            format: int32
                            type: integer
                          hostIP:
                            description: What host IP to bind the external port to.
                            type: string
                          hostPort:
                            description: |-
                              Number of port to expose on the host.
                              If specified, this must be a valid port number, 0 < x < 65536.
                              If HostNetwork is specified, this must match ContainerPort.
                              Most containers do not need this.
                            format: int32
                            type: integer
                          name:
                            description: |-
                              If specified, this must be an IANA_SVC_NAME and unique within the pod. Each
                              named port in a pod must have a unique name. Name for the port that can be
                              referred to by services.
                            type: string
                          protocol:
                            default: TCP
                            description: |-
                              Protocol for port. Must be UDP, TCP, or SCTP.
                              Defaults to "TCP".
                            type: string
                        required:
                        - containerPort
                        type: object
                      type: array
                      x-kubernetes-list-map-keys:
                      - containerPort
                      - protocol
                      x-kubernetes-list-type: map
                    readinessProbe:
                      description: |-
                        Periodic probe of container service readiness.
                        Container will be removed from service endpoints if the probe fails.
                        Cannot be updated.
                        More info: https://kubernetes.
                      properties:
                        exec:
                          description: Exec specifies a command to execute in the
                            container.
                          properties:
                            command:
                              description: |-
                                Command is the command line to execute inside the container, the working directory for the
                                command  is root ('/') in the container's filesystem.
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                          type: object
                        failureThreshold:
                          description: |-
                            Minimum consecutive failures for the probe to be considered failed after having succeeded.
                            Defaults to 3. Minimum value is 1.
                          format: int32
                          type: integer
                        grpc:
                          description: GRPC specifies a GRPC HealthCheckRequest.
                          properties:
                            port:
                              description: Port number of the gRPC service. Number
                                must be in the range 1 to 65535.
                              format: int32
                              type: integer
                            service:
                              default: ""
                              description: |-
                                Service is the name of the service to place in the gRPC HealthCheckRequest
                                (see https://github.com/grpc/grpc/blob/master/doc/health-checking.md).
                              type: string
                          required:
                          - port
                          type: object
                        httpGet:
                          description: HTTPGet specifies an HTTP GET request to perform.
                          properties:
                            host:
                              description: |-
                                Host name to connect to, defaults to the pod IP. You probably want to set
                                "Host" in httpHeaders instead.
                              type: string
                            httpHeaders:
                              description: Custom headers to set in the request. HTTP
                                allows repeated headers.
                              items:
                                description: HTTPHeader describes a custom header
                                  to be used in HTTP probes
                                properties:
                                  name:
                                    description: |-
                                      The header field name.
                                      This will be canonicalized upon output, so case-variant names will be understood as the same header.
                                    type: string
                                  value:
                                    description: The header field value
                                    type: string
                                required:
                                - name
                                - value
                                type: object
                              type: array
                              x-kubernetes-list-type: atomic
                            path:
                              description: Path to access on the HTTP server.
                              type: string
                            port:
                              anyOf:
                              - type: integer
                              - type: string
                              description: |-
                                Name or number of the port to access on the container.
                                Number must be in the range 1 to 65535.
                                Name must be an IANA_SVC_NAME.
                              x-kubernetes-int-or-string: true
                            scheme:
                              description: |-
                                Scheme to use for connecting to the host.
                                Defaults to HTTP.
                              type: string
                          required:
                          - port
                          type: object
                        initialDelaySeconds:
                          description: |-
                            Number of seconds after the container has started before liveness probes are initiated.
                            More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes
                          format: int32
                          type: integer
                        periodSeconds:
                          description: |-
                            How often (in seconds) to perform the probe.
                            Default to 10 seconds. Minimum value is 1.
                          format: int32
                          type: integer
                        successThreshold:
                          description: |-
                            Minimum consecutive successes for the probe to be considered successful after having failed.
                            Defaults to 1. Must be 1 for liveness and startup. Minimum value is 1.
                          format: int32
                          type: integer
                        tcpSocket:
                          description: TCPSocket specifies a connection to a TCP port.
                          properties:
                            host:
                              description: 'Optional: Host name to connect to, defaults
                                to the pod IP.'
                              type: string
                            port:
                              anyOf:
                              - type: integer
                              - type: string
                              description: |-
                                Number or name of the port to access on the container.
                                Number must be in the range 1 to 65535.
                                Name must be an IANA_SVC_NAME.
                              x-kubernetes-int-or-string: true
                          required:
                          - port
                          type: object
                        terminationGracePeriodSeconds:
                          description: Optional duration in seconds the pod needs
                            to terminate gracefully upon probe failure.
                          format: int64
                          type: integer
                        timeoutSeconds:
                          description: |-
                            Number of seconds after which the probe times out.
                            Defaults to 1 second. Minimum value is 1.
                            More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes
                          format: int32
                          type: integer
                      type: object
                    resizePolicy:
                      description: Resources resize policy for the container.
                      items:
                        description: ContainerResizePolicy represents resource resize
                          policy for the container.
                        properties:
                          resourceName:
                            description: |-
                              Name of the resource to which this resource resize policy applies.
                              Supported values: cpu, memory.
                            type: string
                          restartPolicy:
                            description: |-
                              Restart policy to apply when specified resource is resized.
                              If not specified, it defaults to NotRequired.
                            type: string
                        required:
                        - resourceName
                        - restartPolicy
                        type: object
                      type: array
                      x-kubernetes-list-type: atomic
                    resources:
                      description: |-
                        Compute Resources required by this container.
                        Cannot be updated.
                        More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                      properties:
                        claims:
                          description: |-
                            Claims lists the names of resources, defined in spec.resourceClaims,
                            that are used by this container.

                            This is an alpha field and requires enabling the
                            DynamicResourceAllocation feature gate.
                          items:
                            description: ResourceClaim references one entry in PodSpec.ResourceClaims.
                            properties:
                              name:
                                description: |-
                                  Name must match the name of one entry in pod.spec.resourceClaims of
                                  the Pod where this field is used. It makes that resource available
                                  inside a container.
                                type: string
                              request:
                                description: |-
                                  Request is the name chosen for a request in the referenced claim.
                                  If empty, everything from the claim is made available, otherwise
                                  only the result of this request.
                                type: string
                            required:
                            - name
                            type: object
                          type: array
                          x-kubernetes-list-map-keys:
                          - name
                          x-kubernetes-list-type: map
                        limits:
                          additionalProperties:
                            anyOf:
                            - type: integer
                            - type: string
                            pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                            x-kubernetes-int-or-string: true
                          description: |-
                            Limits describes the maximum amount of compute resources allowed.
                            More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                          type: object
                        requests:
                          additionalProperties:
                            anyOf:
                            - type: integer
                            - type: string
                            pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                            x-kubernetes-int-or-string: true
                          description: Requests describes the minimum amount of compute
                            resources required.
                          type: object
                      type: object
                    restartPolicy:
                      description: |-
                        RestartPolicy defines the restart behavior of individual containers in a pod.
                        This field may only be set for init containers, and the only allowed value is "Always".
                      type: string
                    securityContext:
                      description: |-
                        SecurityContext defines the security options the container should be run with.
                        If set, the fields of SecurityContext override the equivalent fields of PodSecurityContext.
                      properties:
                        allowPrivilegeEscalation:
                          description: |-
                            AllowPrivilegeEscalation controls whether a process can gain more
                            privileges than its parent process. This bool directly controls if
                            the no_new_privs flag will be set on the container process.
                          type: boolean
                        appArmorProfile:
                          description: |-
                            appArmorProfile is the AppArmor options to use by this container. If set, this profile
                            overrides the pod's appArmorProfile.
                            Note that this field cannot be set when spec.os.name is windows.
                          properties:
                            localhostProfile:
                              description: |-
                                localhostProfile indicates a profile loaded on the node that should be used.
                                The profile must be preconfigured on the node to work.
                                Must match the loaded name of the profile.
                              type: string
                            type:
                              description: |-
                                type indicates which kind of AppArmor profile will be applied.
                                Valid options are:
                                  Localhost - a profile pre-loaded on the node.
                                  RuntimeDefault - the container runtime's default profile.
                              type: string
                          required:
                          - type
                          type: object
                        capabilities:
                          description: |-
                            The capabilities to add/drop when running containers.
                            Defaults to the default set of capabilities granted by the container runtime.
                            Note that this field cannot be set when spec.os.name is windows.
                          properties:
                            add:
                              description: Added capabilities
                              items:
                                description: Capability represent POSIX capabilities
                                  type
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                            drop:
                              description: Removed capabilities
                              items:
                                description: Capability represent POSIX capabilities
                                  type
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                          type: object
                        privileged:
                          description: |-
                            Run container in privileged mode.
                            Processes in privileged containers are essentially equivalent to root on the host.
                            Defaults to false.
                            Note that this field cannot be set when spec.os.name is windows.
                          type: boolean
                        procMount:
                          description: |-
                            procMount denotes the type of proc mount to use for the containers.
                            The default value is Default which uses the container runtime defaults for
                            readonly paths and masked paths.
                          type: string
                        readOnlyRootFilesystem:
                          description: |-
                            Whether this container has a read-only root filesystem.
                            Default is false.
                            Note that this field cannot be set when spec.os.name is windows.
                          type: boolean
                        runAsGroup:
                          description: |-
                            The GID to run the entrypoint of the container process.
                            Uses runtime default if unset.
                            May also be set in PodSecurityContext.
                          format: int64
                          type: integer
                        runAsNonRoot:
                          description: Indicates that the container must run as a
                            non-root user.
                          type: boolean
                        runAsUser:
                          description: |-
                            The UID to run the entrypoint of the container process.
                            Defaults to user specified in image metadata if unspecified.
                            May also be set in PodSecurityContext.
                          format: int64
                          type: integer
                        seLinuxOptions:
                          description: |-
                            The SELinux context to be applied to the container.
                            If unspecified, the container runtime will allocate a random SELinux context for each
                            container.  May also be set in PodSecurityContext.
                          properties:
                            level:
                              description: Level is SELinux level label that applies
                                to the container.
                              type: string
                            role:
                              description: Role is a SELinux role label that applies
                                to the container.
                              type: string
                            type:
                              description: Type is a SELinux type label that applies
                                to the container.
                              type: string
                            user:
                              description: User is a SELinux user label that applies
                                to the container.
                              type: string
                          type: object
                        seccompProfile:
                          description: |-
                            The seccomp options to use by this container. If seccomp options are
                            provided at both the pod & container level, the container options
                            override the pod options.
                          properties:
                            localhostProfile:
                              description: |-
                                localhostProfile indicates a profile defined in a file on the node should be used.
                                The profile must be preconfigured on the node to work.
                              type: string
                            type:
                              description: |-
                                type indicates which kind of seccomp profile will be applied.
                                Valid options are:

                                Localhost - a profile defined in a file on the node should be used.
                              type: string
                          required:
                          - type
                          type: object
                        windowsOptions:
                          description: |-
                            The Windows specific settings applied to all containers.
                            If unspecified, the options from the PodSecurityContext will be used.
                          properties:
                            gmsaCredentialSpec:
                              description: |-
                                GMSACredentialSpec is where the GMSA admission webhook
                                (https://github.com/kubernetes-sigs/windows-gmsa) inlines the contents of the
                                GMSA credential spec named by the GMSACredentialSpecName field.
                              type: string
                            gmsaCredentialSpecName:
                              description: GMSACredentialSpecName is the name of the
                                GMSA credential spec to use.
                              type: string
                            hostProcess:
                              description: HostProcess determines if a container should
                                be run as a 'Host Process' container.
                              type: boolean
                            runAsUserName:
                              description: |-
                                The UserName in Windows to run the entrypoint of the container process.
                                Defaults to the user specified in image metadata if unspecified.
                                May also be set in PodSecurityContext.
                              type: string
                          type: object
                      type: object
                    startupProbe:
                      description: |-
                        StartupProbe indicates that the Pod has successfully initialized.
                        If specified, no other probes are executed until this completes successfully.
                      properties:
                        exec:
                          description: Exec specifies a command to execute in the
                            container.
                          properties:
                            command:
                              description: |-
                                Command is the command line to execute inside the container, the working directory for the
                                command  is root ('/') in the container's filesystem.
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                          type: object
                        failureThreshold:
                          description: |-
                            Minimum consecutive failures for the probe to be considered failed after having succeeded.
                            Defaults to 3. Minimum value is 1.
                          format: int32
                          type: integer
                        grpc:
                          description: GRPC specifies a GRPC HealthCheckRequest.
                          properties:
                            port:
                              description: Port number of the gRPC service. Number
                                must be in the range 1 to 65535.
                              format: int32
                              type: integer
                            service:
                              default: ""
                              description: |-
                                Service is the name of the service to place in the gRPC HealthCheckRequest
                                (see https://github.com/grpc/grpc/blob/master/doc/health-checking.md).
                              type: string
                          required:
                          - port
                          type: object
                        httpGet:
                          description: HTTPGet specifies an HTTP GET request to perform.
                          properties:
                            host:
                              description: |-
                                Host name to connect to, defaults to the pod IP. You probably want to set
                                "Host" in httpHeaders instead.
                              type: string
                            httpHeaders:
                              description: Custom headers to set in the request. HTTP
                                allows repeated headers.
                              items:
                                description: HTTPHeader describes a custom header
                                  to be used in HTTP probes
                                properties:
                                  name:
                                    description: |-
                                      The header field name.
                                      This will be canonicalized upon output, so case-variant names will be understood as the same header.
                                    type: string
                                  value:
                                    description: The header field value
                                    type: string
                                required:
                                - name
                                - value
                                type: object
                              type: array
                              x-kubernetes-list-type: atomic
                            path:
                              description: Path to access on the HTTP server.
                              type: string
                            port:
                              anyOf:
                              - type: integer
                              - type: string
                              description: |-
                                Name or number of the port to access on the container.
                                Number must be in the range 1 to 65535.
                                Name must be an IANA_SVC_NAME.
                              x-kubernetes-int-or-string: true
                            scheme:
                              description: |-
                                Scheme to use for connecting to the host.
                                Defaults to HTTP.
                              type: string
                          required:
                          - port
                          type: object
                        initialDelaySeconds:
                          description: |-
                            Number of seconds after the container has started before liveness probes are initiated.
                            More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes
                          format: int32
                          type: integer
                        periodSeconds:
                          description: |-
                            How often (in seconds) to perform the probe.
                            Default to 10 seconds. Minimum value is 1.
                          format: int32
                          type: integer
                        successThreshold:
                          description: |-
                            Minimum consecutive successes for the probe to be considered successful after having failed.
                            Defaults to 1. Must be 1 for liveness and startup. Minimum value is 1.
                          format: int32
                          type: integer
                        tcpSocket:
                          description: TCPSocket specifies a connection to a TCP port.
                          properties:
                            host:
                              description: 'Optional: Host name to connect to, defaults
                                to the pod IP.'
                              type: string
                            port:
                              anyOf:
                              - type: integer
                              - type: string
                              description: |-
                                Number or name of the port to access on the container.
                                Number must be in the range 1 to 65535.
                                Name must be an IANA_SVC_NAME.
                              x-kubernetes-int-or-string: true
                          required:
                          - port
                          type: object
                        terminationGracePeriodSeconds:
                          description: Optional duration in seconds the pod needs
                            to terminate gracefully upon probe failure.
                          format: int64
                          type: integer
                        timeoutSeconds:
                          description: |-
                            Number of seconds after which the probe times out.
                            Defaults to 1 second. Minimum value is 1.
                            More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes
                          format: int32
                          type: integer
                      type: object
                    stdin:
                      description: |-
                        Whether this container should allocate a buffer for stdin in the container runtime. If this
                        is not set, reads from stdin in the container will always result in EOF.
                        Default is false.
                      type: boolean
                    stdinOnce:
                      description: |-
                        Whether the container runtime should close the stdin channel after it has been opened by
                        a single attach. When stdin is true the stdin stream will remain open across multiple attach
                        sessions.
                      type: boolean
                    terminationMessagePath:
                      description: |-
                        Optional: Path at which the file to which the container's termination message
                        will be written is mounted into the container's filesystem.
                      type: string
                    terminationMessagePolicy:
                      description: |-
                        Indicate how the termination message should be populated. File will use the contents of
                        terminationMessagePath to populate the container status message on both success and failure.
                      type: string
                    tty:
                      description: |-
                        Whether this container should allocate a TTY for itself, also requires 'stdin' to be true.
                        Default is false.
                      type: boolean
                    volumeDevices:
                      description: volumeDevices is the list of block devices to be
                        used by the container.
                      items:
                        description: volumeDevice describes a mapping of a raw block
                          device within a container.
                        properties:
                          devicePath:
                            description: devicePath is the path inside of the container
                              that the device will be mapped to.
                            type: string
                          name:
                            description: name must match the name of a persistentVolumeClaim
                              in the pod
                            type: string
                        required:
                        - devicePath
                        - name
                        type: object
                      type: array
                      x-kubernetes-list-map-keys:
                      - devicePath
                      x-kubernetes-list-type: map
                    volumeMounts:
                      description: |-
                        Pod volumes to mount into the container's filesystem.
                        Cannot be updated.
                      items:
                        description: VolumeMount describes a mounting of a Volume
                          within a container.
                        properties:
                          mountPath:
                            description: |-
                              Path within the container at which the volume should be mounted.  Must
                              not contain ':'.
                            type: string
                          mountPropagation:
                            description: |-
                              mountPropagation determines how mounts are propagated from the host
                              to container and the other way around.
                              When not set, MountPropagationNone is used.
                              This field is beta in 1.10.
                            type: string
                          name:
                            description: This must match the Name of a Volume.
                            type: string
                          readOnly:
                            description: |-
                              Mounted read-only if true, read-write otherwise (false or unspecified).
                              Defaults to false.
                            type: boolean
                          recursiveReadOnly:
                            description: |-
                              RecursiveReadOnly specifies whether read-only mounts should be handled
                              recursively.

                              If ReadOnly is false, this field has no meaning and must be unspecified.
                            type: string
                          subPath:
                            description: |-
                              Path within the volume from which the container's volume should be mounted.
                              Defaults to "" (volume's root).
                            type: string
                          subPathExpr:
                            description: Expanded path within the volume from which
                              the container's volume should be mounted.
                            type: string
                        required:
                        - mountPath
                        - name
                        type: object
                      type: array
                      x-kubernetes-list-map-keys:
                      - mountPath
                      x-kubernetes-list-type: map
                    workingDir:
                      description: |-
                        Container's working directory.
                        If not specified, the container runtime's default will be used, which
                        might be configured in the container image.
                        Cannot be updated.
                      type: string
                  required:
                  - name
                  type: object
                type: array
              initContainers:
                items:
                  description: A single application container that you want to run
                    within a pod.
                  properties:
                    args:
                      description: |-
                        Arguments to the entrypoint.
                        The container image's CMD is used if this is not provided.
                        Variable references $(VAR_NAME) are expanded using the container's environment.
                      items:
                        type: string
                      type: array
                      x-kubernetes-list-type: atomic
                    command:
                      description: |-
                        Entrypoint array. Not executed within a shell.
                        The container image's ENTRYPOINT is used if this is not provided.
                        Variable references $(VAR_NAME) are expanded using the container's environment.
                      items:
                        type: string
                      type: array
                      x-kubernetes-list-type: atomic
                    env:
                      description: |-
                        List of environment variables to set in the container.
                        Cannot be updated.
                      items:
                        description: EnvVar represents an environment variable present
                          in a Container.
                        properties:
                          name:
                            description: Name of the environment variable. Must be
                              a C_IDENTIFIER.
                            type: string
                          value:
                            description: |-
                              Variable references $(VAR_NAME) are expanded
                              using the previously defined environment variables in the container and
                              any service environment variables.
                            type: string
                          valueFrom:
                            description: Source for the environment variable's value.
                              Cannot be used if value is not empty.
                            properties:
                              configMapKeyRef:
                                description: Selects a key of a ConfigMap.
                                properties:
                                  key:
                                    description: The key to select.
                                    type: string
                                  name:
                                    default: ""
                                    description: |-
                                      Name of the referent.
                                      This field is effectively required, but due to backwards compatibility is
                                      allowed to be empty. Instances of this type with an empty value here are
                                      almost certainly wrong.
                                    type: string
                                  optional:
                                    description: Specify whether the ConfigMap or
                                      its key must be defined
                                    type: boolean
                                required:
                                - key
                                type: object
                                x-kubernetes-map-type: atomic
                              fieldRef:
                                description: |-
                                  Selects a field of the pod: supports metadata.name, metadata.namespace, `metadata.labels['<KEY>']`, `metadata.annotations['<KEY>']`,
                                  spec.nodeName, spec.serviceAccountName, status.hostIP, status.
                                properties:
                                  apiVersion:
                                    description: Version of the schema the FieldPath
                                      is written in terms of, defaults to "v1".
                                    type: string
                                  fieldPath:
                                    description: Path of the field to select in the
                                      specified API version.
                                    type: string
                                required:
                                - fieldPath
                                type: object
                                x-kubernetes-map-type: atomic
                              resourceFieldRef:
                                description: |-
                                  Selects a resource of the container: only resources limits and requests
                                  (limits.cpu, limits.memory, limits.ephemeral-storage, requests.cpu, requests.memory and requests.
                                properties:
                                  containerName:
                                    description: 'Container name: required for volumes,
                                      optional for env vars'
                                    type: string
                                  divisor:
                                    anyOf:
                                    - type: integer
                                    - type: string
                                    description: Specifies the output format of the
                                      exposed resources, defaults to "1"
                                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                    x-kubernetes-int-or-string: true
                                  resource:
                                    description: 'Required: resource to select'
                                    type: string
                                required:
                                - resource
                                type: object
                                x-kubernetes-map-type: atomic
                              secretKeyRef:
                                description: Selects a key of a secret in the pod's
                                  namespace
                                properties:
                                  key:
                                    description: The key of the secret to select from.  Must
                                      be a valid secret key.
                                    type: string
                                  name:
                                    default: ""
                                    description: |-
                                      Name of the referent.
                                      This field is effectively required, but due to backwards compatibility is
                                      allowed to be empty. Instances of this type with an empty value here are
                                      almost certainly wrong.
                                    type: string
                                  optional:
                                    description: Specify whether the Secret or its
                                      key must be defined
                                    type: boolean
                                required:
                                - key
                                type: object
                                x-kubernetes-map-type: atomic
                            type: object
                        required:
                        - name
                        type: object
                      type: array
                      x-kubernetes-list-map-keys:
                      - name
                      x-kubernetes-list-type: map
                    envFrom:
                      description: |-
                        List of sources to populate environment variables in the container.
                        The keys defined within a source must be a C_IDENTIFIER.
                      items:
                        description: EnvFromSource represents the source of a set
                          of ConfigMaps or Secrets
                        properties:
                          configMapRef:
                            description: The ConfigMap to select from
                            properties:
                              name:
                                default: ""
                                description: |-
                                  Name of the referent.
                                  This field is effectively required, but due to backwards compatibility is
                                  allowed to be empty. Instances of this type with an empty value here are
                                  almost certainly wrong.
                                type: string
                              optional:
                                description: Specify whether the ConfigMap must be
                                  defined
                                type: boolean
                            type: object
                            x-kubernetes-map-type: atomic
                          prefix:
                            description: Optional text to prepend to the name of each
                              environment variable. Must be a C_IDENTIFIER.
                            type: string
                          secretRef:
                            description: The Secret to select from
                            properties:
                              name:
                                default: ""
                                description: |-
                                  Name of the referent.
                                  This field is effectively required, but due to backwards compatibility is
                                  allowed to be empty. Instances of this type with an empty value here are
                                  almost certainly wrong.
                                type: string
                              optional:
                                description: Specify whether the Secret must be defined
                                type: boolean
                            type: object
                            x-kubernetes-map-type: atomic
                        type: object
                      type: array
                      x-kubernetes-list-type: atomic
                    image:
                      description: |-
                        Container image name.
                        More info: https://kubernetes.
                      type: string
                    imagePullPolicy:
                      description: |-
                        Image pull policy.
                        One of Always, Never, IfNotPresent.
                        Defaults to Always if :latest tag is specified, or IfNotPresent otherwise.
                        Cannot be updated.
                        More info: https://kubernetes.
                      type: string
                    lifecycle:
                      description: |-
                        Actions that the management system should take in response to container lifecycle events.
                        Cannot be updated.
                      properties:
                        postStart:
                          description: |-
                            PostStart is called immediately after a container is created. If the handler fails,
                            the container is terminated and restarted according to its restart policy.
                          properties:
                            exec:
                              description: Exec specifies a command to execute in
                                the container.
                              properties:
                                command:
                                  description: |-
                                    Command is the command line to execute inside the container, the working directory for the
                                    command  is root ('/') in the container's filesystem.
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                              type: object
                            httpGet:
                              description: HTTPGet specifies an HTTP GET request to
                                perform.
                              properties:
                                host:
                                  description: |-
                                    Host name to connect to, defaults to the pod IP. You probably want to set
                                    "Host" in httpHeaders instead.
                                  type: string
                                httpHeaders:
                                  description: Custom headers to set in the request.
                                    HTTP allows repeated headers.
                                  items:
                                    description: HTTPHeader describes a custom header
                                      to be used in HTTP probes
                                    properties:
                                      name:
                                        description: |-
                                          The header field name.
                                          This will be canonicalized upon output, so case-variant names will be understood as the same header.
                                        type: string
                                      value:
                                        description: The header field value
                                        type: string
                                    required:
                                    - name
                                    - value
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                path:
                                  description: Path to access on the HTTP server.
                                  type: string
                                port:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  description: |-
                                    Name or number of the port to access on the container.
                                    Number must be in the range 1 to 65535.
                                    Name must be an IANA_SVC_NAME.
                                  x-kubernetes-int-or-string: true
                                scheme:
                                  description: |-
                                    Scheme to use for connecting to the host.
                                    Defaults to HTTP.
                                  type: string
                              required:
                              - port
                              type: object
                            sleep:
                              description: Sleep represents a duration that the container
                                should sleep.
                              properties:
                                seconds:
                                  description: Seconds is the number of seconds to
                                    sleep.
                                  format: int64
                                  type: integer
                              required:
                              - seconds
                              type: object
                            tcpSocket:
                              description: |-
                                Deprecated. TCPSocket is NOT supported as a LifecycleHandler and kept
                                for backward compatibility. There is no validation of this field and
                                lifecycle hooks will fail at runtime when it is specified.
                              properties:
                                host:
                                  description: 'Optional: Host name to connect to,
                                    defaults to the pod IP.'
                                  type: string
                                port:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  description: |-
                                    Number or name of the port to access on the container.
                                    Number must be in the range 1 to 65535.
                                    Name must be an IANA_SVC_NAME.
                                  x-kubernetes-int-or-string: true
                              required:
                              - port
                              type: object
                          type: object
                        preStop:
                          description: |-
                            PreStop is called immediately before a container is terminated due to an
                            API request or management event such as liveness/startup probe failure,
                            preemption, resource contention, etc.
                          properties:
                            exec:
                              description: Exec specifies a command to execute in
                                the container.
                              properties:
                                command:
                                  description: |-
                                    Command is the command line to execute inside the container, the working directory for the
                                    command  is root ('/') in the container's filesystem.
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                              type: object
                            httpGet:
                              description: HTTPGet specifies an HTTP GET request to
                                perform.
                              properties:
                                host:
                                  description: |-
                                    Host name to connect to, defaults to the pod IP. You probably want to set
                                    "Host" in httpHeaders instead.
                                  type: string
                                httpHeaders:
                                  description: Custom headers to set in the request.
                                    HTTP allows repeated headers.
                                  items:
                                    description: HTTPHeader describes a custom header
                                      to be used in HTTP probes
                                    properties:
                                      name:
                                        description: |-
                                          The header field name.
                                          This will be canonicalized upon output, so case-variant names will be understood as the same header.
                                        type: string
                                      value:
                                        description: The header field value
                                        type: string
                                    required:
                                    - name
                                    - value
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                path:
                                  description: Path to access on the HTTP server.
                                  type: string
                                port:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  description: |-
                                    Name or number of the port to access on the container.
                                    Number must be in the range 1 to 65535.
                                    Name must be an IANA_SVC_NAME.
                                  x-kubernetes-int-or-string: true
                                scheme:
                                  description: |-
                                    Scheme to use for connecting to the host.
                                    Defaults to HTTP.
                                  type: string
                              required:
                              - port
                              type: object
                            sleep:
                              description: Sleep represents a duration that the container
                                should sleep.
                              properties:
                                seconds:
                                  description: Seconds is the number of seconds to
                                    sleep.
                                  format: int64
                                  type: integer
                              required:
                              - seconds
                              type: object
                            tcpSocket:
                              description: |-
                                Deprecated. TCPSocket is NOT supported as a LifecycleHandler and kept
                                for backward compatibility. There is no validation of this field and
                                lifecycle hooks will fail at runtime when it is specified.
                              properties:
                                host:
                                  description: 'Optional: Host name to connect to,
                                    defaults to the pod IP.'
                                  type: string
                                port:
                                  anyOf:
                                  - type: integer
                                  - type: string
                                  description: |-
                                    Number or name of the port to access on the container.
                                    Number must be in the range 1 to 65535.
                                    Name must be an IANA_SVC_NAME.
                                  x-kubernetes-int-or-string: true
                              required:
                              - port
                              type: object
                          type: object
                        stopSignal:
                          description: |-
                            StopSignal defines which signal will be sent to a container when it is being stopped.
                            If not specified, the default is defined by the container runtime in use.
                          type: string
                      type: object
                    livenessProbe:
                      description: |-
                        Periodic probe of container liveness.
                        Container will be restarted if the probe fails.
                        Cannot be updated.
                        More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes
                      properties:
                        exec:
                          description: Exec specifies a command to execute in the
                            container.
                          properties:
                            command:
                              description: |-
                                Command is the command line to execute inside the container, the working directory for the
                                command  is root ('/') in the container's filesystem.
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                          type: object
                        failureThreshold:
                          description: |-
                            Minimum consecutive failures for the probe to be considered failed after having succeeded.
                            Defaults to 3. Minimum value is 1.
                          format: int32
                          type: integer
                        grpc:
                          description: GRPC specifies a GRPC HealthCheckRequest.
                          properties:
                            port:
                              description: Port number of the gRPC service. Number
                                must be in the range 1 to 65535.
                              format: int32
                              type: integer
                            service:
                              default: ""
                              description: |-
                                Service is the name of the service to place in the gRPC HealthCheckRequest
                                (see https://github.com/grpc/grpc/blob/master/doc/health-checking.md).
                              type: string
                          required:
                          - port
                          type: object
                        httpGet:
                          description: HTTPGet specifies an HTTP GET request to perform.
                          properties:
                            host:
                              description: |-
                                Host name to connect to, defaults to the pod IP. You probably want to set
                                "Host" in httpHeaders instead.
                              type: string
                            httpHeaders:
                              description: Custom headers to set in the request. HTTP
                                allows repeated headers.
                              items:
                                description: HTTPHeader describes a custom header
                                  to be used in HTTP probes
                                properties:
                                  name:
                                    description: |-
                                      The header field name.
                                      This will be canonicalized upon output, so case-variant names will be understood as the same header.
                                    type: string
                                  value:
                                    description: The header field value
                                    type: string
                                required:
                                - name
                                - value
                                type: object
                              type: array
                              x-kubernetes-list-type: atomic
                            path:
                              description: Path to access on the HTTP server.
                              type: string
                            port:
                              anyOf:
                              - type: integer
                              - type: string
                              description: |-
                                Name or number of the port to access on the container.
                                Number must be in the range 1 to 65535.
                                Name must be an IANA_SVC_NAME.
                              x-kubernetes-int-or-string: true
                            scheme:
                              description: |-
                                Scheme to use for connecting to the host.
                                Defaults to HTTP.
                              type: string
                          required:
                          - port
                          type: object
                        initialDelaySeconds:
                          description: |-
                            Number of seconds after the container has started before liveness probes are initiated.
                            More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes
                          format: int32
                          type: integer
                        periodSeconds:
                          description: |-
                            How often (in seconds) to perform the probe.
                            Default to 10 seconds. Minimum value is 1.
                          format: int32
                          type: integer
                        successThreshold:
                          description: |-
                            Minimum consecutive successes for the probe to be considered successful after having failed.
                            Defaults to 1. Must be 1 for liveness and startup. Minimum value is 1.
                          format: int32
                          type: integer
                        tcpSocket:
                          description: TCPSocket specifies a connection to a TCP port.
                          properties:
                            host:
                              description: 'Optional: Host name to connect to, defaults
                                to the pod IP.'
                              type: string
                            port:
                              anyOf:
                              - type: integer
                              - type: string
                              description: |-
                                Number or name of the port to access on the container.
                                Number must be in the range 1 to 65535.
                                Name must be an IANA_SVC_NAME.
                              x-kubernetes-int-or-string: true
                          required:
                          - port
                          type: object
                        terminationGracePeriodSeconds:
                          description: Optional duration in seconds the pod needs
                            to terminate gracefully upon probe failure.
                          format: int64
                          type: integer
                        timeoutSeconds:
                          description: |-
                            Number of seconds after which the probe times out.
                            Defaults to 1 second. Minimum value is 1.
                            More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes
                          format: int32
                          type: integer
                      type: object
                    name:
                      description: |-
                        Name of the container specified as a DNS_LABEL.
                        Each container in a pod must have a unique name (DNS_LABEL).
                        Cannot be updated.
                      type: string
                    ports:
                      description: |-
                        List of ports to expose from the container. Not specifying a port here
                        DOES NOT prevent that port from being exposed. Any port which is
                        listening on the default "0.0.0.
                      items:
                        description: ContainerPort represents a network port in a
                          single container.
                        properties:
                          containerPort:
                            description: |-
                              Number of port to expose on the pod's IP address.
                              This must be a valid port number, 0 < x < 65536.
                            format: int32
                            type: integer
                          hostIP:
                            description: What host IP to bind the external port to.
                            type: string
                          hostPort:
                            description: |-
                              Number of port to expose on the host.
                              If specified, this must be a valid port number, 0 < x < 65536.
                              If HostNetwork is specified, this must match ContainerPort.
                              Most containers do not need this.
                            format: int32
                            type: integer
                          name:
                            description: |-
                              If specified, this must be an IANA_SVC_NAME and unique within the pod. Each
                              named port in a pod must have a unique name. Name for the port that can be
                              referred to by services.
                            type: string
                          protocol:
                            default: TCP
                            description: |-
                              Protocol for port. Must be UDP, TCP, or SCTP.
                              Defaults to "TCP".
                            type: string
                        required:
                        - containerPort
                        type: object
                      type: array
                      x-kubernetes-list-map-keys:
                      - containerPort
                      - protocol
                      x-kubernetes-list-type: map
                    readinessProbe:
                      description: |-
                        Periodic probe of container service readiness.
                        Container will be removed from service endpoints if the probe fails.
                        Cannot be updated.
                        More info: https://kubernetes.
                      properties:
                        exec:
                          description: Exec specifies a command to execute in the
                            container.
                          properties:
                            command:
                              description: |-
                                Command is the command line to execute inside the container, the working directory for the
                                command  is root ('/') in the container's filesystem.
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                          type: object
                        failureThreshold:
                          description: |-
                            Minimum consecutive failures for the probe to be considered failed after having succeeded.
                            Defaults to 3. Minimum value is 1.
                          format: int32
                          type: integer
                        grpc:
                          description: GRPC specifies a GRPC HealthCheckRequest.
                          properties:
                            port:
                              description: Port number of the gRPC service. Number
                                must be in the range 1 to 65535.
                              format: int32
                              type: integer
                            service:
                              default: ""
                              description: |-
                                Service is the name of the service to place in the gRPC HealthCheckRequest
                                (see https://github.com/grpc/grpc/blob/master/doc/health-checking.md).
                              type: string
                          required:
                          - port
                          type: object
                        httpGet:
                          description: HTTPGet specifies an HTTP GET request to perform.
                          properties:
                            host:
                              description: |-
                                Host name to connect to, defaults to the pod IP. You probably want to set
                                "Host" in httpHeaders instead.
                              type: string
                            httpHeaders:
                              description: Custom headers to set in the request. HTTP
                                allows repeated headers.
                              items:
                                description: HTTPHeader describes a custom header
                                  to be used in HTTP probes
                                properties:
                                  name:
                                    description: |-
                                      The header field name.
                                      This will be canonicalized upon output, so case-variant names will be understood as the same header.
                                    type: string
                                  value:
                                    description: The header field value
                                    type: string
                                required:
                                - name
                                - value
                                type: object
                              type: array
                              x-kubernetes-list-type: atomic
                            path:
                              description: Path to access on the HTTP server.
                              type: string
                            port:
                              anyOf:
                              - type: integer
                              - type: string
                              description: |-
                                Name or number of the port to access on the container.
                                Number must be in the range 1 to 65535.
                                Name must be an IANA_SVC_NAME.
                              x-kubernetes-int-or-string: true
                            scheme:
                              description: |-
                                Scheme to use for connecting to the host.
                                Defaults to HTTP.
                              type: string
                          required:
                          - port
                          type: object
                        initialDelaySeconds:
                          description: |-
                            Number of seconds after the container has started before liveness probes are initiated.
                            More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes
                          format: int32
                          type: integer
                        periodSeconds:
                          description: |-
                            How often (in seconds) to perform the probe.
                            Default to 10 seconds. Minimum value is 1.
                          format: int32
                          type: integer
                        successThreshold:
                          description: |-
                            Minimum consecutive successes for the probe to be considered successful after having failed.
                            Defaults to 1. Must be 1 for liveness and startup. Minimum value is 1.
                          format: int32
                          type: integer
                        tcpSocket:
                          description: TCPSocket specifies a connection to a TCP port.
                          properties:
                            host:
                              description: 'Optional: Host name to connect to, defaults
                                to the pod IP.'
                              type: string
                            port:
                              anyOf:
                              - type: integer
                              - type: string
                              description: |-
                                Number or name of the port to access on the container.
                                Number must be in the range 1 to 65535.
                                Name must be an IANA_SVC_NAME.
                              x-kubernetes-int-or-string: true
                          required:
                          - port
                          type: object
                        terminationGracePeriodSeconds:
                          description: Optional duration in seconds the pod needs
                            to terminate gracefully upon probe failure.
                          format: int64
                          type: integer
                        timeoutSeconds:
                          description: |-
                            Number of seconds after which the probe times out.
                            Defaults to 1 second. Minimum value is 1.
                            More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes
                          format: int32
                          type: integer
                      type: object
                    resizePolicy:
                      description: Resources resize policy for the container.
                      items:
                        description: ContainerResizePolicy represents resource resize
                          policy for the container.
                        properties:
                          resourceName:
                            description: |-
                              Name of the resource to which this resource resize policy applies.
                              Supported values: cpu, memory.
                            type: string
                          restartPolicy:
                            description: |-
                              Restart policy to apply when specified resource is resized.
                              If not specified, it defaults to NotRequired.
                            type: string
                        required:
                        - resourceName
                        - restartPolicy
                        type: object
                      type: array
                      x-kubernetes-list-type: atomic
                    resources:
                      description: |-
                        Compute Resources required by this container.
                        Cannot be updated.
                        More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                      properties:
                        claims:
                          description: |-
                            Claims lists the names of resources, defined in spec.resourceClaims,
                            that are used by this container.

                            This is an alpha field and requires enabling the
                            DynamicResourceAllocation feature gate.
                          items:
                            description: ResourceClaim references one entry in PodSpec.ResourceClaims.
                            properties:
                              name:
                                description: |-
                                  Name must match the name of one entry in pod.spec.resourceClaims of
                                  the Pod where this field is used. It makes that resource available
                                  inside a container.
                                type: string
                              request:
                                description: |-
                                  Request is the name chosen for a request in the referenced claim.
                                  If empty, everything from the claim is made available, otherwise
                                  only the result of this request.
                                type: string
                            required:
                            - name
                            type: object
                          type: array
                          x-kubernetes-list-map-keys:
                          - name
                          x-kubernetes-list-type: map
                        limits:
                          additionalProperties:
                            anyOf:
                            - type: integer
                            - type: string
                            pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                            x-kubernetes-int-or-string: true
                          description: |-
                            Limits describes the maximum amount of compute resources allowed.
                            More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                          type: object
                        requests:
                          additionalProperties:
                            anyOf:
                            - type: integer
                            - type: string
                            pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                            x-kubernetes-int-or-string: true
                          description: Requests describes the minimum amount of compute
                            resources required.
                          type: object
                      type: object
                    restartPolicy:
                      description: |-
                        RestartPolicy defines the restart behavior of individual containers in a pod.
                        This field may only be set for init containers, and the only allowed value is "Always".
                      type: string
                    securityContext:
                      description: |-
                        SecurityContext defines the security options the container should be run with.
                        If set, the fields of SecurityContext override the equivalent fields of PodSecurityContext.
                      properties:
                        allowPrivilegeEscalation:
                          description: |-
                            AllowPrivilegeEscalation controls whether a process can gain more
                            privileges than its parent process. This bool directly controls if
                            the no_new_privs flag will be set on the container process.
                          type: boolean
                        appArmorProfile:
                          description: |-
                            appArmorProfile is the AppArmor options to use by this container. If set, this profile
                            overrides the pod's appArmorProfile.
                            Note that this field cannot be set when spec.os.name is windows.
                          properties:
                            localhostProfile:
                              description: |-
                                localhostProfile indicates a profile loaded on the node that should be used.
                                The profile must be preconfigured on the node to work.
                                Must match the loaded name of the profile.
                              type: string
                            type:
                              description: |-
                                type indicates which kind of AppArmor profile will be applied.
                                Valid options are:
                                  Localhost - a profile pre-loaded on the node.
                                  RuntimeDefault - the container runtime's default profile.
                              type: string
                          required:
                          - type
                          type: object
                        capabilities:
                          description: |-
                            The capabilities to add/drop when running containers.
                            Defaults to the default set of capabilities granted by the container runtime.
                            Note that this field cannot be set when spec.os.name is windows.
                          properties:
                            add:
                              description: Added capabilities
                              items:
                                description: Capability represent POSIX capabilities
                                  type
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                            drop:
                              description: Removed capabilities
                              items:
                                description: Capability represent POSIX capabilities
                                  type
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                          type: object
                        privileged:
                          description: |-
                            Run container in privileged mode.
                            Processes in privileged containers are essentially equivalent to root on the host.
                            Defaults to false.
                            Note that this field cannot be set when spec.os.name is windows.
                          type: boolean
                        procMount:
                          description: |-
                            procMount denotes the type of proc mount to use for the containers.
                            The default value is Default which uses the container runtime defaults for
                            readonly paths and masked paths.
                          type: string
                        readOnlyRootFilesystem:
                          description: |-
                            Whether this container has a read-only root filesystem.
                            Default is false.
                            Note that this field cannot be set when spec.os.name is windows.
                          type: boolean
                        runAsGroup:
                          description: |-
                            The GID to run the entrypoint of the container process.
                            Uses runtime default if unset.
                            May also be set in PodSecurityContext.
                          format: int64
                          type: integer
                        runAsNonRoot:
                          description: Indicates that the container must run as a
                            non-root user.
                          type: boolean
                        runAsUser:
                          description: |-
                            The UID to run the entrypoint of the container process.
                            Defaults to user specified in image metadata if unspecified.
                            May also be set in PodSecurityContext.
                          format: int64
                          type: integer
                        seLinuxOptions:
                          description: |-
                            The SELinux context to be applied to the container.
                            If unspecified, the container runtime will allocate a random SELinux context for each
                            container.  May also be set in PodSecurityContext.
                          properties:
                            level:
                              description: Level is SELinux level label that applies
                                to the container.
                              type: string
                            role:
                              description: Role is a SELinux role label that applies
                                to the container.
                              type: string
                            type:
                              description: Type is a SELinux type label that applies
                                to the container.
                              type: string
                            user:
                              description: User is a SELinux user label that applies
                                to the container.
                              type: string
                          type: object
                        seccompProfile:
                          description: |-
                            The seccomp options to use by this container. If seccomp options are
                            provided at both the pod & container level, the container options
                            override the pod options.
                          properties:
                            localhostProfile:
                              description: |-
                                localhostProfile indicates a profile defined in a file on the node should be used.
                                The profile must be preconfigured on the node to work.
                              type: string
                            type:
                              description: |-
                                type indicates which kind of seccomp profile will be applied.
                                Valid options are:

                                Localhost - a profile defined in a file on the node should be used.
                              type: string
                          required:
                          - type
                          type: object
                        windowsOptions:
                          description: |-
                            The Windows specific settings applied to all containers.
                            If unspecified, the options from the PodSecurityContext will be used.
                          properties:
                            gmsaCredentialSpec:
                              description: |-
                                GMSACredentialSpec is where the GMSA admission webhook
                                (https://github.com/kubernetes-sigs/windows-gmsa) inlines the contents of the
                                GMSA credential spec named by the GMSACredentialSpecName field.
                              type: string
                            gmsaCredentialSpecName:
                              description: GMSACredentialSpecName is the name of the
                                GMSA credential spec to use.
                              type: string
                            hostProcess:
                              description: HostProcess determines if a container should
                                be run as a 'Host Process' container.
                              type: boolean
                            runAsUserName:
                              description: |-
                                The UserName in Windows to run the entrypoint of the container process.
                                Defaults to the user specified in image metadata if unspecified.
                                May also be set in PodSecurityContext.
                              type: string
                          type: object
                      type: object
                    startupProbe:
                      description: |-
                        StartupProbe indicates that the Pod has successfully initialized.
                        If specified, no other probes are executed until this completes successfully.
                      properties:
                        exec:
                          description: Exec specifies a command to execute in the
                            container.
                          properties:
                            command:
                              description: |-
                                Command is the command line to execute inside the container, the working directory for the
                                command  is root ('/') in the container's filesystem.
                              items:
                                type: string
                              type: array
                              x-kubernetes-list-type: atomic
                          type: object
                        failureThreshold:
                          description: |-
                            Minimum consecutive failures for the probe to be considered failed after having succeeded.
                            Defaults to 3. Minimum value is 1.
                          format: int32
                          type: integer
                        grpc:
                          description: GRPC specifies a GRPC HealthCheckRequest.
                          properties:
                            port:
                              description: Port number of the gRPC service. Number
                                must be in the range 1 to 65535.
                              format: int32
                              type: integer
                            service:
                              default: ""
                              description: |-
                                Service is the name of the service to place in the gRPC HealthCheckRequest
                                (see https://github.com/grpc/grpc/blob/master/doc/health-checking.md).
                              type: string
                          required:
                          - port
                          type: object
                        httpGet:
                          description: HTTPGet specifies an HTTP GET request to perform.
                          properties:
                            host:
                              description: |-
                                Host name to connect to, defaults to the pod IP. You probably want to set
                                "Host" in httpHeaders instead.
                              type: string
                            httpHeaders:
                              description: Custom headers to set in the request. HTTP
                                allows repeated headers.
                              items:
                                description: HTTPHeader describes a custom header
                                  to be used in HTTP probes
                                properties:
                                  name:
                                    description: |-
                                      The header field name.
                                      This will be canonicalized upon output, so case-variant names will be understood as the same header.
                                    type: string
                                  value:
                                    description: The header field value
                                    type: string
                                required:
                                - name
                                - value
                                type: object
                              type: array
                              x-kubernetes-list-type: atomic
                            path:
                              description: Path to access on the HTTP server.
                              type: string
                            port:
                              anyOf:
                              - type: integer
                              - type: string
                              description: |-
                                Name or number of the port to access on the container.
                                Number must be in the range 1 to 65535.
                                Name must be an IANA_SVC_NAME.
                              x-kubernetes-int-or-string: true
                            scheme:
                              description: |-
                                Scheme to use for connecting to the host.
                                Defaults to HTTP.
                              type: string
                          required:
                          - port
                          type: object
                        initialDelaySeconds:
                          description: |-
                            Number of seconds after the container has started before liveness probes are initiated.
                            More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes
                          format: int32
                          type: integer
                        periodSeconds:
                          description: |-
                            How often (in seconds) to perform the probe.
                            Default to 10 seconds. Minimum value is 1.
                          format: int32
                          type: integer
                        successThreshold:
                          description: |-
                            Minimum consecutive successes for the probe to be considered successful after having failed.
                            Defaults to 1. Must be 1 for liveness and startup. Minimum value is 1.
                          format: int32
                          type: integer
                        tcpSocket:
                          description: TCPSocket specifies a connection to a TCP port.
                          properties:
                            host:
                              description: 'Optional: Host name to connect to, defaults
                                to the pod IP.'
                              type: string
                            port:
                              anyOf:
                              - type: integer
                              - type: string
                              description: |-
                                Number or name of the port to access on the container.
                                Number must be in the range 1 to 65535.
                                Name must be an IANA_SVC_NAME.
                              x-kubernetes-int-or-string: true
                          required:
                          - port
                          type: object
                        terminationGracePeriodSeconds:
                          description: Optional duration in seconds the pod needs
                            to terminate gracefully upon probe failure.
                          format: int64
                          type: integer
                        timeoutSeconds:
                          description: |-
                            Number of seconds after which the probe times out.
                            Defaults to 1 second. Minimum value is 1.
                            More info: https://kubernetes.io/docs/concepts/workloads/pods/pod-lifecycle#container-probes
                          format: int32
                          type: integer
                      type: object
                    stdin:
                      description: |-
                        Whether this container should allocate a buffer for stdin in the container runtime. If this
                        is not set, reads from stdin in the container will always result in EOF.
                        Default is false.
                      type: boolean
                    stdinOnce:
                      description: |-
                        Whether the container runtime should close the stdin channel after it has been opened by
                        a single attach. When stdin is true the stdin stream will remain open across multiple attach
                        sessions.
                      type: boolean
                    terminationMessagePath:
                      description: |-
                        Optional: Path at which the file to which the container's termination message
                        will be written is mounted into the container's filesystem.
                      type: string
                    terminationMessagePolicy:
                      description: |-
                        Indicate how the termination message should be populated. File will use the contents of
                        terminationMessagePath to populate the container status message on both success and failure.
                      type: string
                    tty:
                      description: |-
                        Whether this container should allocate a TTY for itself, also requires 'stdin' to be true.
                        Default is false.
                      type: boolean
                    volumeDevices:
                      description: volumeDevices is the list of block devices to be
                        used by the container.
                      items:
                        description: volumeDevice describes a mapping of a raw block
                          device within a container.
                        properties:
                          devicePath:
                            description: devicePath is the path inside of the container
                              that the device will be mapped to.
                            type: string
                          name:
                            description: name must match the name of a persistentVolumeClaim
                              in the pod
                            type: string
                        required:
                        - devicePath
                        - name
                        type: object
                      type: array
                      x-kubernetes-list-map-keys:
                      - devicePath
                      x-kubernetes-list-type: map
                    volumeMounts:
                      description: |-
                        Pod volumes to mount into the container's filesystem.
                        Cannot be updated.
                      items:
                        description: VolumeMount describes a mounting of a Volume
                          within a container.
                        properties:
                          mountPath:
                            description: |-
                              Path within the container at which the volume should be mounted.  Must
                              not contain ':'.
                            type: string
                          mountPropagation:
                            description: |-
                              mountPropagation determines how mounts are propagated from the host
                              to container and the other way around.
                              When not set, MountPropagationNone is used.
                              This field is beta in 1.10.
                            type: string
                          name:
                            description: This must match the Name of a Volume.
                            type: string
                          readOnly:
                            description: |-
                              Mounted read-only if true, read-write otherwise (false or unspecified).
                              Defaults to false.
                            type: boolean
                          recursiveReadOnly:
                            description: |-
                              RecursiveReadOnly specifies whether read-only mounts should be handled
                              recursively.

                              If ReadOnly is false, this field has no meaning and must be unspecified.
                            type: string
                          subPath:
                            description: |-
                              Path within the volume from which the container's volume should be mounted.
                              Defaults to "" (volume's root).
                            type: string
                          subPathExpr:
                            description: Expanded path within the volume from which
                              the container's volume should be mounted.
                            type: string
                        required:
                        - mountPath
                        - name
                        type: object
                      type: array
                      x-kubernetes-list-map-keys:
                      - mountPath
                      x-kubernetes-list-type: map
                    workingDir:
                      description: |-
                        Container's working directory.
                        If not specified, the container runtime's default will be used, which
                        might be configured in the container image.
                        Cannot be updated.
                      type: string
                  required:
                  - name
                  type: object
                type: array
              updateStrategy:
                default: NoUpdate
                enum:
                - NoUpdate
                - RollingUpdate
                type: string
              volumes:
                items:
                  description: Volume represents a named volume in a pod that may
                    be accessed by any container in the pod.
                  properties:
                    awsElasticBlockStore:
                      description: |-
                        awsElasticBlockStore represents an AWS Disk resource that is attached to a
                        kubelet's host machine and then exposed to the pod.
                        Deprecated: AWSElasticBlockStore is deprecated.
                      properties:
                        fsType:
                          description: |-
                            fsType is the filesystem type of the volume that you want to mount.
                            Tip: Ensure that the filesystem type is supported by the host operating system.
                            Examples: "ext4", "xfs", "ntfs".
                          type: string
                        partition:
                          description: |-
                            partition is the partition in the volume that you want to mount.
                            If omitted, the default is to mount by volume name.
                            Examples: For volume /dev/sda1, you specify the partition as "1".
                          format: int32
                          type: integer
                        readOnly:
                          description: |-
                            readOnly value true will force the readOnly setting in VolumeMounts.
                            More info: https://kubernetes.io/docs/concepts/storage/volumes#awselasticblockstore
                          type: boolean
                        volumeID:
                          description: |-
                            volumeID is unique ID of the persistent disk resource in AWS (Amazon EBS volume).
                            More info: https://kubernetes.io/docs/concepts/storage/volumes#awselasticblockstore
                          type: string
                      required:
                      - volumeID
                      type: object
                    azureDisk:
                      description: |-
                        azureDisk represents an Azure Data Disk mount on the host and bind mount to the pod.
                        Deprecated: AzureDisk is deprecated. All operations for the in-tree azureDisk type
                        are redirected to the disk.csi.
                      properties:
                        cachingMode:
                          description: 'cachingMode is the Host Caching mode: None,
                            Read Only, Read Write.'
                          type: string
                        diskName:
                          description: diskName is the Name of the data disk in the
                            blob storage
                          type: string
                        diskURI:
                          description: diskURI is the URI of data disk in the blob
                            storage
                          type: string
                        fsType:
                          default: ext4
                          description: |-
                            fsType is Filesystem type to mount.
                            Must be a filesystem type supported by the host operating system.
                            Ex. "ext4", "xfs", "ntfs". Implicitly inferred to be "ext4" if unspecified.
                          type: string
                        kind:
                          description: 'kind expected values are Shared: multiple
                            blob disks per storage account  Dedicated: single blob
                            disk per storage account  Managed: azure managed data
                            disk (only in managed availability set).'
                          type: string
                        readOnly:
                          default: false
                          description: |-
                            readOnly Defaults to false (read/write). ReadOnly here will force
                            the ReadOnly setting in VolumeMounts.
                          type: boolean
                      required:
                      - diskName
                      - diskURI
                      type: object
                    azureFile:
                      description: |-
                        azureFile represents an Azure File Service mount on the host and bind mount to the pod.
                        Deprecated: AzureFile is deprecated. All operations for the in-tree azureFile type
                        are redirected to the file.
                      properties:
                        readOnly:
                          description: |-
                            readOnly defaults to false (read/write). ReadOnly here will force
                            the ReadOnly setting in VolumeMounts.
                          type: boolean
                        secretName:
                          description: secretName is the  name of secret that contains
                            Azure Storage Account Name and Key
                          type: string
                        shareName:
                          description: shareName is the azure share Name
                          type: string
                      required:
                      - secretName
                      - shareName
                      type: object
                    cephfs:
                      description: |-
                        cephFS represents a Ceph FS mount on the host that shares a pod's lifetime.
                        Deprecated: CephFS is deprecated and the in-tree cephfs type is no longer supported.
                      properties:
                        monitors:
                          description: |-
                            monitors is Required: Monitors is a collection of Ceph monitors
                            More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        path:
                          description: 'path is Optional: Used as the mounted root,
                            rather than the full Ceph tree, default is /'
                          type: string
                        readOnly:
                          description: |-
                            readOnly is Optional: Defaults to false (read/write). ReadOnly here will force
                            the ReadOnly setting in VolumeMounts.
                            More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it
                          type: boolean
                        secretFile:
                          description: |-
                            secretFile is Optional: SecretFile is the path to key ring for User, default is /etc/ceph/user.secret
                            More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it
                          type: string
                        secretRef:
                          description: |-
                            secretRef is Optional: SecretRef is reference to the authentication secret for User, default is empty.
                            More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it
                          properties:
                            name:
                              default: ""
                              description: |-
                                Name of the referent.
                                This field is effectively required, but due to backwards compatibility is
                                allowed to be empty. Instances of this type with an empty value here are
                                almost certainly wrong.
                              type: string
                          type: object
                          x-kubernetes-map-type: atomic
                        user:
                          description: |-
                            user is optional: User is the rados user name, default is admin
                            More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it
                          type: string
                      required:
                      - monitors
                      type: object
                    cinder:
                      description: |-
                        cinder represents a cinder volume attached and mounted on kubelets host machine.
                        Deprecated: Cinder is deprecated. All operations for the in-tree cinder type
                        are redirected to the cinder.csi.
                      properties:
                        fsType:
                          description: |-
                            fsType is the filesystem type to mount.
                            Must be a filesystem type supported by the host operating system.
                            Examples: "ext4", "xfs", "ntfs". Implicitly inferred to be "ext4" if unspecified.
                          type: string
                        readOnly:
                          description: |-
                            readOnly defaults to false (read/write). ReadOnly here will force
                            the ReadOnly setting in VolumeMounts.
                            More info: https://examples.k8s.io/mysql-cinder-pd/README.md
                          type: boolean
                        secretRef:
                          description: |-
                            secretRef is optional: points to a secret object containing parameters used to connect
                            to OpenStack.
                          properties:
                            name:
                              default: ""
                              description: |-
                                Name of the referent.
                                This field is effectively required, but due to backwards compatibility is
                                allowed to be empty. Instances of this type with an empty value here are
                                almost certainly wrong.
                              type: string
                          type: object
                          x-kubernetes-map-type: atomic
                        volumeID:
                          description: |-
                            volumeID used to identify the volume in cinder.
                            More info: https://examples.k8s.io/mysql-cinder-pd/README.md
                          type: string
                      required:
                      - volumeID
                      type: object
                    configMap:
                      description: configMap represents a configMap that should populate
                        this volume
                      properties:
                        defaultMode:
                          description: |-
                            defaultMode is optional: mode bits used to set permissions on created files by default.
                            Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511.
                          format: int32
                          type: integer
                        items:
                          description: |-
                            items if unspecified, each key-value pair in the Data field of the referenced
                            ConfigMap will be projected into the volume as a file whose name is the
                            key and content is the value.
                          items:
                            description: Maps a string key to a path within a volume.
                            properties:
                              key:
                                description: key is the key to project.
                                type: string
                              mode:
                                description: |-
                                  mode is Optional: mode bits used to set permissions on this file.
                                  Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511.
                                format: int32
                                type: integer
                              path:
                                description: |-
                                  path is the relative path of the file to map the key to.
                                  May not be an absolute path.
                                  May not contain the path element '..'.
                                  May not start with the string '..'.
                                type: string
                            required:
                            - key
                            - path
                            type: object
                          type: array
                          x-kubernetes-list-type: atomic
                        name:
                          default: ""
                          description: |-
                            Name of the referent.
                            This field is effectively required, but due to backwards compatibility is
                            allowed to be empty. Instances of this type with an empty value here are
                            almost certainly wrong.
                          type: string
                        optional:
                          description: optional specify whether the ConfigMap or its
                            keys must be defined
                          type: boolean
                      type: object
                      x-kubernetes-map-type: atomic
                    csi:
                      description: csi (Container Storage Interface) represents ephemeral
                        storage that is handled by certain external CSI drivers.
                      properties:
                        driver:
                          description: |-
                            driver is the name of the CSI driver that handles this volume.
                            Consult with your admin for the correct name as registered in the cluster.
                          type: string
                        fsType:
                          description: |-
                            fsType to mount. Ex. "ext4", "xfs", "ntfs".
                            If not provided, the empty value is passed to the associated CSI driver
                            which will determine the default filesystem to apply.
                          type: string
                        nodePublishSecretRef:
                          description: |-
                            nodePublishSecretRef is a reference to the secret object containing
                            sensitive information to pass to the CSI driver to complete the CSI
                            NodePublishVolume and NodeUnpublishVolume calls.
                          properties:
                            name:
                              default: ""
                              description: |-
                                Name of the referent.
                                This field is effectively required, but due to backwards compatibility is
                                allowed to be empty. Instances of this type with an empty value here are
                                almost certainly wrong.
                              type: string
                          type: object
                          x-kubernetes-map-type: atomic
                        readOnly:
                          description: |-
                            readOnly specifies a read-only configuration for the volume.
                            Defaults to false (read/write).
                          type: boolean
                        volumeAttributes:
                          additionalProperties:
                            type: string
                          description: |-
                            volumeAttributes stores driver-specific properties that are passed to the CSI
                            driver. Consult your driver's documentation for supported values.
                          type: object
                      required:
                      - driver
                      type: object
                    downwardAPI:
                      description: downwardAPI represents downward API about the pod
                        that should populate this volume
                      properties:
                        defaultMode:
                          description: |-
                            Optional: mode bits to use on created files by default. Must be a
                            Optional: mode bits used to set permissions on created files by default.
                          format: int32
                          type: integer
                        items:
                          description: Items is a list of downward API volume file
                          items:
                            description: DownwardAPIVolumeFile represents information
                              to create the file containing the pod field
                            properties:
                              fieldRef:
                                description: 'Required: Selects a field of the pod:
                                  only annotations, labels, name, namespace and uid
                                  are supported.'
                                properties:
                                  apiVersion:
                                    description: Version of the schema the FieldPath
                                      is written in terms of, defaults to "v1".
                                    type: string
                                  fieldPath:
                                    description: Path of the field to select in the
                                      specified API version.
                                    type: string
                                required:
                                - fieldPath
                                type: object
                                x-kubernetes-map-type: atomic
                              mode:
                                description: |-
                                  Optional: mode bits used to set permissions on this file, must be an octal value
                                  between 0000 and 0777 or a decimal value between 0 and 511.
                                format: int32
                                type: integer
                              path:
                                description: 'Required: Path is  the relative path
                                  name of the file to be created. Must not be absolute
                                  or contain the ''..'' path. Must be utf-8 encoded.
                                  The first item of the relative path must not start
                                  with ''..'''
                                type: string
                              resourceFieldRef:
                                description: |-
                                  Selects a resource of the container: only resources limits and requests
                                  (limits.cpu, limits.memory, requests.cpu and requests.memory) are currently supported.
                                properties:
                                  containerName:
                                    description: 'Container name: required for volumes,
                                      optional for env vars'
                                    type: string
                                  divisor:
                                    anyOf:
                                    - type: integer
                                    - type: string
                                    description: Specifies the output format of the
                                      exposed resources, defaults to "1"
                                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                    x-kubernetes-int-or-string: true
                                  resource:
                                    description: 'Required: resource to select'
                                    type: string
                                required:
                                - resource
                                type: object
                                x-kubernetes-map-type: atomic
                            required:
                            - path
                            type: object
                          type: array
                          x-kubernetes-list-type: atomic
                      type: object
                    emptyDir:
                      description: |-
                        emptyDir represents a temporary directory that shares a pod's lifetime.
                        More info: https://kubernetes.io/docs/concepts/storage/volumes#emptydir
                      properties:
                        medium:
                          description: |-
                            medium represents what type of storage medium should back this directory.
                            The default is "" which means to use the node's default medium.
                            Must be an empty string (default) or Memory.
                          type: string
                        sizeLimit:
                          anyOf:
                          - type: integer
                          - type: string
                          description: |-
                            sizeLimit is the total amount of local storage required for this EmptyDir volume.
                            The size limit is also applicable for memory medium.
                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                          x-kubernetes-int-or-string: true
                      type: object
                    ephemeral:
                      description: ephemeral represents a volume that is handled by
                        a cluster storage driver.
                      properties:
                        volumeClaimTemplate:
                          description: |-
                            Will be used to create a stand-alone PVC to provision the volume.
                            The pod in which this EphemeralVolumeSource is embedded will be the
                            owner of the PVC, i.e.
                          properties:
                            metadata:
                              description: |-
                                May contain labels and annotations that will be copied into the PVC
                                when creating it. No other fields are allowed and will be rejected during
                                validation.
                              properties:
                                annotations:
                                  additionalProperties:
                                    type: string
                                  type: object
                                finalizers:
                                  items:
                                    type: string
                                  type: array
                                labels:
                                  additionalProperties:
                                    type: string
                                  type: object
                                name:
                                  type: string
                                namespace:
                                  type: string
                              type: object
                            spec:
                              description: |-
                                The specification for the PersistentVolumeClaim. The entire content is
                                copied unchanged into the PVC that gets created from this
                                template.
                              properties:
                                accessModes:
                                  description: |-
                                    accessModes contains the desired access modes the volume should have.
                                    More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#access-modes-1
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                dataSource:
                                  description: |-
                                    dataSource field can be used to specify either:
                                    * An existing VolumeSnapshot object (snapshot.storage.k8s.
                                  properties:
                                    apiGroup:
                                      description: |-
                                        APIGroup is the group for the resource being referenced.
                                        If APIGroup is not specified, the specified Kind must be in the core API group.
                                        For any other third-party types, APIGroup is required.
                                      type: string
                                    kind:
                                      description: Kind is the type of resource being
                                        referenced
                                      type: string
                                    name:
                                      description: Name is the name of resource being
                                        referenced
                                      type: string
                                  required:
                                  - kind
                                  - name
                                  type: object
                                  x-kubernetes-map-type: atomic
                                dataSourceRef:
                                  description: |-
                                    dataSourceRef specifies the object from which to populate the volume with data, if a non-empty
                                    volume is desired.
                                  properties:
                                    apiGroup:
                                      description: |-
                                        APIGroup is the group for the resource being referenced.
                                        If APIGroup is not specified, the specified Kind must be in the core API group.
                                        For any other third-party types, APIGroup is required.
                                      type: string
                                    kind:
                                      description: Kind is the type of resource being
                                        referenced
                                      type: string
                                    name:
                                      description: Name is the name of resource being
                                        referenced
                                      type: string
                                    namespace:
                                      description: |-
                                        Namespace is the namespace of resource being referenced
                                        Note that when a namespace is specified, a gateway.networking.k8s.
                                      type: string
                                  required:
                                  - kind
                                  - name
                                  type: object
                                resources:
                                  description: resources represents the minimum resources
                                    the volume should have.
                                  properties:
                                    limits:
                                      additionalProperties:
                                        anyOf:
                                        - type: integer
                                        - type: string
                                        pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                        x-kubernetes-int-or-string: true
                                      description: |-
                                        Limits describes the maximum amount of compute resources allowed.
                                        More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/
                                      type: object
                                    requests:
                                      additionalProperties:
                                        anyOf:
                                        - type: integer
                                        - type: string
                                        pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                        x-kubernetes-int-or-string: true
                                      description: Requests describes the minimum
                                        amount of compute resources required.
                                      type: object
                                  type: object
                                selector:
                                  description: selector is a label query over volumes
                                    to consider for binding.
                                  properties:
                                    matchExpressions:
                                      description: matchExpressions is a list of label
                                        selector requirements. The requirements are
                                        ANDed.
                                      items:
                                        description: |-
                                          A label selector requirement is a selector that contains values, a key, and an operator that
                                          relates the key and values.
                                        properties:
                                          key:
                                            description: key is the label key that
                                              the selector applies to.
                                            type: string
                                          operator:
                                            description: |-
                                              operator represents a key's relationship to a set of values.
                                              Valid operators are In, NotIn, Exists and DoesNotExist.
                                            type: string
                                          values:
                                            description: |-
                                              values is an array of string values. If the operator is In or NotIn,
                                              the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                              the values array must be empty.
                                            items:
                                              type: string
                                            type: array
                                            x-kubernetes-list-type: atomic
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                      x-kubernetes-list-type: atomic
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      description: matchLabels is a map of {key,value}
                                        pairs.
                                      type: object
                                  type: object
                                  x-kubernetes-map-type: atomic
                                storageClassName:
                                  description: |-
                                    storageClassName is the name of the StorageClass required by the claim.
                                    More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#class-1
                                  type: string
                                volumeAttributesClassName:
                                  description: volumeAttributesClassName may be used
                                    to set the VolumeAttributesClass used by this
                                    claim.
                                  type: string
                                volumeMode:
                                  description: |-
                                    volumeMode defines what type of volume is required by the claim.
                                    Value of Filesystem is implied when not included in claim spec.
                                  type: string
                                volumeName:
                                  description: volumeName is the binding reference
                                    to the PersistentVolume backing this claim.
                                  type: string
                              type: object
                          required:
                          - spec
                          type: object
                      type: object
                    fc:
                      description: fc represents a Fibre Channel resource that is
                        attached to a kubelet's host machine and then exposed to the
                        pod.
                      properties:
                        fsType:
                          description: |-
                            fsType is the filesystem type to mount.
                            Must be a filesystem type supported by the host operating system.
                            Ex. "ext4", "xfs", "ntfs". Implicitly inferred to be "ext4" if unspecified.
                          type: string
                        lun:
                          description: 'lun is Optional: FC target lun number'
                          format: int32
                          type: integer
                        readOnly:
                          description: |-
                            readOnly is Optional: Defaults to false (read/write). ReadOnly here will force
                            the ReadOnly setting in VolumeMounts.
                          type: boolean
                        targetWWNs:
                          description: 'targetWWNs is Optional: FC target worldwide
                            names (WWNs)'
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        wwids:
                          description: |-
                            wwids Optional: FC volume world wide identifiers (wwids)
                            Either wwids or combination of targetWWNs and lun must be set, but not both simultaneously.
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                      type: object
                    flexVolume:
                      description: |-
                        flexVolume represents a generic volume resource that is
                        provisioned/attached using an exec based plugin.
                        Deprecated: FlexVolume is deprecated. Consider using a CSIDriver instead.
                      properties:
                        driver:
                          description: driver is the name of the driver to use for
                            this volume.
                          type: string
                        fsType:
                          description: |-
                            fsType is the filesystem type to mount.
                            Must be a filesystem type supported by the host operating system.
                            Ex. "ext4", "xfs", "ntfs". The default filesystem depends on FlexVolume script.
                          type: string
                        options:
                          additionalProperties:
                            type: string
                          description: 'options is Optional: this field holds extra
                            command options if any.'
                          type: object
                        readOnly:
                          description: |-
                            readOnly is Optional: defaults to false (read/write). ReadOnly here will force
                            the ReadOnly setting in VolumeMounts.
                          type: boolean
                        secretRef:
                          description: |-
                            secretRef is Optional: secretRef is reference to the secret object containing
                            sensitive information to pass to the plugin scripts. This may be
                            empty if no secret object is specified.
                          properties:
                            name:
                              default: ""
                              description: |-
                                Name of the referent.
                                This field is effectively required, but due to backwards compatibility is
                                allowed to be empty. Instances of this type with an empty value here are
                                almost certainly wrong.
                              type: string
                          type: object
                          x-kubernetes-map-type: atomic
                      required:
                      - driver
                      type: object
                    flocker:
                      description: flocker represents a Flocker volume attached to
                        a kubelet's host machine. This depends on the Flocker control
                        service being running.
                      properties:
                        datasetName:
                          description: |-
                            datasetName is Name of the dataset stored as metadata -> name on the dataset for Flocker
                            should be considered as deprecated
                          type: string
                        datasetUUID:
                          description: datasetUUID is the UUID of the dataset. This
                            is unique identifier of a Flocker dataset
                          type: string
                      type: object
                    gcePersistentDisk:
                      description: |-
                        gcePersistentDisk represents a GCE Disk resource that is attached to a
                        kubelet's host machine and then exposed to the pod.
                        Deprecated: GCEPersistentDisk is deprecated.
                      properties:
                        fsType:
                          description: |-
                            fsType is filesystem type of the volume that you want to mount.
                            Tip: Ensure that the filesystem type is supported by the host operating system.
                            Examples: "ext4", "xfs", "ntfs".
                          type: string
                        partition:
                          description: |-
                            partition is the partition in the volume that you want to mount.
                            If omitted, the default is to mount by volume name.
                            Examples: For volume /dev/sda1, you specify the partition as "1".
                          format: int32
                          type: integer
                        pdName:
                          description: |-
                            pdName is unique name of the PD resource in GCE. Used to identify the disk in GCE.
                            More info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk
                          type: string
                        readOnly:
                          description: |-
                            readOnly here will force the ReadOnly setting in VolumeMounts.
                            Defaults to false.
                            More info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk
                          type: boolean
                      required:
                      - pdName
                      type: object
                    gitRepo:
                      description: |-
                        gitRepo represents a git repository at a particular revision.
                        Deprecated: GitRepo is deprecated.
                      properties:
                        directory:
                          description: |-
                            directory is the target directory name.
                            Must not contain or start with '..'.  If '.' is supplied, the volume directory will be the
                            git repository.
                          type: string
                        repository:
                          description: repository is the URL
                          type: string
                        revision:
                          description: revision is the commit hash for the specified
                            revision.
                          type: string
                      required:
                      - repository
                      type: object
                    glusterfs:
                      description: |-
                        glusterfs represents a Glusterfs mount on the host that shares a pod's lifetime.
                        Deprecated: Glusterfs is deprecated and the in-tree glusterfs type is no longer supported.
                        More info: https://examples.
                      properties:
                        endpoints:
                          description: |-
                            endpoints is the endpoint name that details Glusterfs topology.
                            More info: https://examples.k8s.io/volumes/glusterfs/README.md#create-a-pod
                          type: string
                        path:
                          description: |-
                            path is the Glusterfs volume path.
                            More info: https://examples.k8s.io/volumes/glusterfs/README.md#create-a-pod
                          type: string
                        readOnly:
                          description: |-
                            readOnly here will force the Glusterfs volume to be mounted with read-only permissions.
                            Defaults to false.
                            More info: https://examples.k8s.io/volumes/glusterfs/README.md#create-a-pod
                          type: boolean
                      required:
                      - endpoints
                      - path
                      type: object
                    hostPath:
                      description: |-
                        hostPath represents a pre-existing file or directory on the host
                        machine that is directly exposed to the container.
                      properties:
                        path:
                          description: |-
                            path of the directory on the host.
                            If the path is a symlink, it will follow the link to the real path.
                            More info: https://kubernetes.io/docs/concepts/storage/volumes#hostpath
                          type: string
                        type:
                          description: |-
                            type for HostPath Volume
                            Defaults to ""
                            More info: https://kubernetes.io/docs/concepts/storage/volumes#hostpath
                          type: string
                      required:
                      - path
                      type: object
                    image:
                      description: image represents an OCI object (a container image
                        or artifact) pulled and mounted on the kubelet's host machine.
                      properties:
                        pullPolicy:
                          description: |-
                            Policy for pulling OCI objects. Possible values are:
                            Always: the kubelet always attempts to pull the reference. Container creation will fail If the pull fails.
                          type: string
                        reference:
                          description: |-
                            Required: Image or artifact reference to be used.
                            Behaves in the same way as pod.spec.containers[*].image.
                          type: string
                      type: object
                    iscsi:
                      description: |-
                        iscsi represents an ISCSI Disk resource that is attached to a
                        kubelet's host machine and then exposed to the pod.
                        More info: https://examples.k8s.io/volumes/iscsi/README.md
                      properties:
                        chapAuthDiscovery:
                          description: chapAuthDiscovery defines whether support iSCSI
                            Discovery CHAP authentication
                          type: boolean
                        chapAuthSession:
                          description: chapAuthSession defines whether support iSCSI
                            Session CHAP authentication
                          type: boolean
                        fsType:
                          description: |-
                            fsType is the filesystem type of the volume that you want to mount.
                            Tip: Ensure that the filesystem type is supported by the host operating system.
                            Examples: "ext4", "xfs", "ntfs".
                          type: string
                        initiatorName:
                          description: initiatorName is the custom iSCSI Initiator
                            Name.
                          type: string
                        iqn:
                          description: iqn is the target iSCSI Qualified Name.
                          type: string
                        iscsiInterface:
                          default: default
                          description: |-
                            iscsiInterface is the interface Name that uses an iSCSI transport.
                            Defaults to 'default' (tcp).
                          type: string
                        lun:
                          description: lun represents iSCSI Target Lun number.
                          format: int32
                          type: integer
                        portals:
                          description: |-
                            portals is the iSCSI Target Portal List. The portal is either an IP or ip_addr:port if the port
                            is other than default (typically TCP ports 860 and 3260).
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        readOnly:
                          description: |-
                            readOnly here will force the ReadOnly setting in VolumeMounts.
                            Defaults to false.
                          type: boolean
                        secretRef:
                          description: secretRef is the CHAP Secret for iSCSI target
                            and initiator authentication
                          properties:
                            name:
                              default: ""
                              description: |-
                                Name of the referent.
                                This field is effectively required, but due to backwards compatibility is
                                allowed to be empty. Instances of this type with an empty value here are
                                almost certainly wrong.
                              type: string
                          type: object
                          x-kubernetes-map-type: atomic
                        targetPortal:
                          description: |-
                            targetPortal is iSCSI Target Portal. The Portal is either an IP or ip_addr:port if the port
                            is other than default (typically TCP ports 860 and 3260).
                          type: string
                      required:
                      - iqn
                      - lun
                      - targetPortal
                      type: object
                    name:
                      description: |-
                        name of the volume.
                        Must be a DNS_LABEL and unique within the pod.
                        More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names
                      type: string
                    nfs:
                      description: |-
                        nfs represents an NFS mount on the host that shares a pod's lifetime
                        More info: https://kubernetes.io/docs/concepts/storage/volumes#nfs
                      properties:
                        path:
                          description: |-
                            path that is exported by the NFS server.
                            More info: https://kubernetes.io/docs/concepts/storage/volumes#nfs
                          type: string
                        readOnly:
                          description: |-
                            readOnly here will force the NFS export to be mounted with read-only permissions.
                            Defaults to false.
                            More info: https://kubernetes.io/docs/concepts/storage/volumes#nfs
                          type: boolean
                        server:
                          description: |-
                            server is the hostname or IP address of the NFS server.
                            More info: https://kubernetes.io/docs/concepts/storage/volumes#nfs
                          type: string
                      required:
                      - path
                      - server
                      type: object
                    persistentVolumeClaim:
                      description: |-
                        persistentVolumeClaimVolumeSource represents a reference to a
                        PersistentVolumeClaim in the same namespace.
                        More info: https://kubernetes.
                      properties:
                        claimName:
                          description: |-
                            claimName is the name of a PersistentVolumeClaim in the same namespace as the pod using this volume.
                            More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims
                          type: string
                        readOnly:
                          description: |-
                            readOnly Will force the ReadOnly setting in VolumeMounts.
                            Default false.
                          type: boolean
                      required:
                      - claimName
                      type: object
                    photonPersistentDisk:
                      description: photonPersistentDisk represents a PhotonController
                        persistent disk attached and mounted on kubelets host machine.
                      properties:
                        fsType:
                          description: |-
                            fsType is the filesystem type to mount.
                            Must be a filesystem type supported by the host operating system.
                            Ex. "ext4", "xfs", "ntfs". Implicitly inferred to be "ext4" if unspecified.
                          type: string
                        pdID:
                          description: pdID is the ID that identifies Photon Controller
                            persistent disk
                          type: string
                      required:
                      - pdID
                      type: object
                    portworxVolume:
                      description: |-
                        portworxVolume represents a portworx volume attached and mounted on kubelets host machine.
                        Deprecated: PortworxVolume is deprecated.
                      properties:
                        fsType:
                          description: |-
                            fSType represents the filesystem type to mount
                            Must be a filesystem type supported by the host operating system.
                            Ex. "ext4", "xfs". Implicitly inferred to be "ext4" if unspecified.
                          type: string
                        readOnly:
                          description: |-
                            readOnly defaults to false (read/write). ReadOnly here will force
                            the ReadOnly setting in VolumeMounts.
                          type: boolean
                        volumeID:
                          description: volumeID uniquely identifies a Portworx volume
                          type: string
                      required:
                      - volumeID
                      type: object
                    projected:
                      description: projected items for all in one resources secrets,
                        configmaps, and downward API
                      properties:
                        defaultMode:
                          description: |-
                            defaultMode are the mode bits used to set permissions on created files by default.
                            Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511.
                          format: int32
                          type: integer
                        sources:
                          description: |-
                            sources is the list of volume projections. Each entry in this list
                            handles one source.
                          items:
                            description: |-
                              Projection that may be projected along with other supported volume types.
                              Exactly one of these fields must be set.
                            properties:
                              clusterTrustBundle:
                                description: |-
                                  ClusterTrustBundle allows a pod to access the `.spec.trustBundle` field
                                  of ClusterTrustBundle objects in an auto-updating file.

                                  Alpha, gated by the ClusterTrustBundleProjection feature gate.
                                properties:
                                  labelSelector:
                                    description: |-
                                      Select all ClusterTrustBundles that match this label selector.  Only has
                                      effect if signerName is set.  Mutually-exclusive with name.  If unset,
                                      interpreted as "match nothing".
                                    properties:
                                      matchExpressions:
                                        description: matchExpressions is a list of
                                          label selector requirements. The requirements
                                          are ANDed.
                                        items:
                                          description: |-
                                            A label selector requirement is a selector that contains values, a key, and an operator that
                                            relates the key and values.
                                          properties:
                                            key:
                                              description: key is the label key that
                                                the selector applies to.
                                              type: string
                                            operator:
                                              description: |-
                                                operator represents a key's relationship to a set of values.
                                                Valid operators are In, NotIn, Exists and DoesNotExist.
                                              type: string
                                            values:
                                              description: |-
                                                values is an array of string values. If the operator is In or NotIn,
                                                the values array must be non-empty. If the operator is Exists or DoesNotExist,
                                                the values array must be empty.
                                              items:
                                                type: string
                                              type: array
                                              x-kubernetes-list-type: atomic
                                          required:
                                          - key
                                          - operator
                                          type: object
                                        type: array
                                        x-kubernetes-list-type: atomic
                                      matchLabels:
                                        additionalProperties:
                                          type: string
                                        description: matchLabels is a map of {key,value}
                                          pairs.
                                        type: object
                                    type: object
                                    x-kubernetes-map-type: atomic
                                  name:
                                    description: |-
                                      Select a single ClusterTrustBundle by object name.  Mutually-exclusive
                                      with signerName and labelSelector.
                                    type: string
                                  optional:
                                    description: |-
                                      If true, don't block pod startup if the referenced ClusterTrustBundle(s)
                                      aren't available.  If using name, then the named ClusterTrustBundle is
                                      allowed not to exist.
                                    type: boolean
                                  path:
                                    description: Relative path from the volume root
                                      to write the bundle.
                                    type: string
                                  signerName:
                                    description: |-
                                      Select all ClusterTrustBundles that match this signer name.
                                      Mutually-exclusive with name.  The contents of all selected
                                      ClusterTrustBundles will be unified and deduplicated.
                                    type: string
                                required:
                                - path
                                type: object
                              configMap:
                                description: configMap information about the configMap
                                  data to project
                                properties:
                                  items:
                                    description: |-
                                      items if unspecified, each key-value pair in the Data field of the referenced
                                      ConfigMap will be projected into the volume as a file whose name is the
                                      key and content is the value.
                                    items:
                                      description: Maps a string key to a path within
                                        a volume.
                                      properties:
                                        key:
                                          description: key is the key to project.
                                          type: string
                                        mode:
                                          description: |-
                                            mode is Optional: mode bits used to set permissions on this file.
                                            Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511.
                                          format: int32
                                          type: integer
                                        path:
                                          description: |-
                                            path is the relative path of the file to map the key to.
                                            May not be an absolute path.
                                            May not contain the path element '..'.
                                            May not start with the string '..'.
                                          type: string
                                      required:
                                      - key
                                      - path
                                      type: object
                                    type: array
                                    x-kubernetes-list-type: atomic
                                  name:
                                    default: ""
                                    description: |-
                                      Name of the referent.
                                      This field is effectively required, but due to backwards compatibility is
                                      allowed to be empty. Instances of this type with an empty value here are
                                      almost certainly wrong.
                                    type: string
                                  optional:
                                    description: optional specify whether the ConfigMap
                                      or its keys must be defined
                                    type: boolean
                                type: object
                                x-kubernetes-map-type: atomic
                              downwardAPI:
                                description: downwardAPI information about the downwardAPI
                                  data to project
                                properties:
                                  items:
                                    description: Items is a list of DownwardAPIVolume
                                      file
                                    items:
                                      description: DownwardAPIVolumeFile represents
                                        information to create the file containing
                                        the pod field
                                      properties:
                                        fieldRef:
                                          description: 'Required: Selects a field
                                            of the pod: only annotations, labels,
                                            name, namespace and uid are supported.'
                                          properties:
                                            apiVersion:
                                              description: Version of the schema the
                                                FieldPath is written in terms of,
                                                defaults to "v1".
                                              type: string
                                            fieldPath:
                                              description: Path of the field to select
                                                in the specified API version.
                                              type: string
                                          required:
                                          - fieldPath
                                          type: object
                                          x-kubernetes-map-type: atomic
                                        mode:
                                          description: |-
                                            Optional: mode bits used to set permissions on this file, must be an octal value
                                            between 0000 and 0777 or a decimal value between 0 and 511.
                                          format: int32
                                          type: integer
                                        path:
                                          description: 'Required: Path is  the relative
                                            path name of the file to be created. Must
                                            not be absolute or contain the ''..''
                                            path. Must be utf-8 encoded. The first
                                            item of the relative path must not start
                                            with ''..'''
                                          type: string
                                        resourceFieldRef:
                                          description: |-
                                            Selects a resource of the container: only resources limits and requests
                                            (limits.cpu, limits.memory, requests.cpu and requests.memory) are currently supported.
                                          properties:
                                            containerName:
                                              description: 'Container name: required
                                                for volumes, optional for env vars'
                                              type: string
                                            divisor:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              description: Specifies the output format
                                                of the exposed resources, defaults
                                                to "1"
                                              pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                              x-kubernetes-int-or-string: true
                                            resource:
                                              description: 'Required: resource to
                                                select'
                                              type: string
                                          required:
                                          - resource
                                          type: object
                                          x-kubernetes-map-type: atomic
                                      required:
                                      - path
                                      type: object
                                    type: array
                                    x-kubernetes-list-type: atomic
                                type: object
                              secret:
                                description: secret information about the secret data
                                  to project
                                properties:
                                  items:
                                    description: |-
                                      items if unspecified, each key-value pair in the Data field of the referenced
                                      Secret will be projected into the volume as a file whose name is the
                                      key and content is the value.
                                    items:
                                      description: Maps a string key to a path within
                                        a volume.
                                      properties:
                                        key:
                                          description: key is the key to project.
                                          type: string
                                        mode:
                                          description: |-
                                            mode is Optional: mode bits used to set permissions on this file.
                                            Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511.
                                          format: int32
                                          type: integer
                                        path:
                                          description: |-
                                            path is the relative path of the file to map the key to.
                                            May not be an absolute path.
                                            May not contain the path element '..'.
                                            May not start with the string '..'.
                                          type: string
                                      required:
                                      - key
                                      - path
                                      type: object
                                    type: array
                                    x-kubernetes-list-type: atomic
                                  name:
                                    default: ""
                                    description: |-
                                      Name of the referent.
                                      This field is effectively required, but due to backwards compatibility is
                                      allowed to be empty. Instances of this type with an empty value here are
                                      almost certainly wrong.
                                    type: string
                                  optional:
                                    description: optional field specify whether the
                                      Secret or its key must be defined
                                    type: boolean
                                type: object
                                x-kubernetes-map-type: atomic
                              serviceAccountToken:
                                description: serviceAccountToken is information about
                                  the serviceAccountToken data to project
                                properties:
                                  audience:
                                    description: |-
                                      audience is the intended audience of the token. A recipient of a token
                                      must identify itself with an identifier specified in the audience of the
                                      token, and otherwise should reject the token.
                                    type: string
                                  expirationSeconds:
                                    description: |-
                                      expirationSeconds is the requested duration of validity of the service
                                      account token. As the token approaches expiration, the kubelet volume
                                      plugin will proactively rotate the service account token.
                                    format: int64
                                    type: integer
                                  path:
                                    description: |-
                                      path is the path relative to the mount point of the file to project the
                                      token into.
                                    type: string
                                required:
                                - path
                                type: object
                            type: object
                          type: array
                          x-kubernetes-list-type: atomic
                      type: object
                    quobyte:
                      description: |-
                        quobyte represents a Quobyte mount on the host that shares a pod's lifetime.
                        Deprecated: Quobyte is deprecated and the in-tree quobyte type is no longer supported.
                      properties:
                        group:
                          description: |-
                            group to map volume access to
                            Default is no group
                          type: string
                        readOnly:
                          description: |-
                            readOnly here will force the Quobyte volume to be mounted with read-only permissions.
                            Defaults to false.
                          type: boolean
                        registry:
                          description: |-
                            registry represents a single or multiple Quobyte Registry services
                            specified as a string as host:port pair (multiple entries are separated with commas)
                            which acts as the central registry for volumes
                          type: string
                        tenant:
                          description: |-
                            tenant owning the given Quobyte volume in the Backend
                            Used with dynamically provisioned Quobyte volumes, value is set by the plugin
                          type: string
                        user:
                          description: |-
                            user to map volume access to
                            Defaults to serivceaccount user
                          type: string
                        volume:
                          description: volume is a string that references an already
                            created Quobyte volume by name.
                          type: string
                      required:
                      - registry
                      - volume
                      type: object
                    rbd:
                      description: |-
                        rbd represents a Rados Block Device mount on the host that shares a pod's lifetime.
                        Deprecated: RBD is deprecated and the in-tree rbd type is no longer supported.
                        More info: https://examples.k8s.
                      properties:
                        fsType:
                          description: |-
                            fsType is the filesystem type of the volume that you want to mount.
                            Tip: Ensure that the filesystem type is supported by the host operating system.
                            Examples: "ext4", "xfs", "ntfs".
                          type: string
                        image:
                          description: |-
                            image is the rados image name.
                            More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it
                          type: string
                        keyring:
                          default: /etc/ceph/keyring
                          description: |-
                            keyring is the path to key ring for RBDUser.
                            Default is /etc/ceph/keyring.
                            More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it
                          type: string
                        monitors:
                          description: |-
                            monitors is a collection of Ceph monitors.
                            More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it
                          items:
                            type: string
                          type: array
                          x-kubernetes-list-type: atomic
                        pool:
                          default: rbd
                          description: |-
                            pool is the rados pool name.
                            Default is rbd.
                            More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it
                          type: string
                        readOnly:
                          description: |-
                            readOnly here will force the ReadOnly setting in VolumeMounts.
                            Defaults to false.
                            More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it
                          type: boolean
                        secretRef:
                          description: |-
                            secretRef is name of the authentication secret for RBDUser. If provided
                            overrides keyring.
                            Default is nil.
                            More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it
                          properties:
                            name:
                              default: ""
                              description: |-
                                Name of the referent.
                                This field is effectively required, but due to backwards compatibility is
                                allowed to be empty. Instances of this type with an empty value here are
                                almost certainly wrong.
                              type: string
                          type: object
                          x-kubernetes-map-type: atomic
                        user:
                          default: admin
                          description: |-
                            user is the rados user name.
                            Default is admin.
                            More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it
                          type: string
                      required:
                      - image
                      - monitors
                      type: object
                    scaleIO:
                      description: |-
                        scaleIO represents a ScaleIO persistent volume attached and mounted on Kubernetes nodes.
                        Deprecated: ScaleIO is deprecated and the in-tree scaleIO type is no longer supported.
                      properties:
                        fsType:
                          default: xfs
                          description: |-
                            fsType is the filesystem type to mount.
                            Must be a filesystem type supported by the host operating system.
                            Ex. "ext4", "xfs", "ntfs".
                            Default is "xfs".
                          type: string
                        gateway:
                          description: gateway is the host address of the ScaleIO
                            API Gateway.
                          type: string
                        protectionDomain:
                          description: protectionDomain is the name of the ScaleIO
                            Protection Domain for the configured storage.
                          type: string
                        readOnly:
                          description: |-
                            readOnly Defaults to false (read/write). ReadOnly here will force
                            the ReadOnly setting in VolumeMounts.
                          type: boolean
                        secretRef:
                          description: |-
                            secretRef references to the secret for ScaleIO user and other
                            sensitive information. If this is not provided, Login operation will fail.
                          properties:
                            name:
                              default: ""
                              description: |-
                                Name of the referent.
                                This field is effectively required, but due to backwards compatibility is
                                allowed to be empty. Instances of this type with an empty value here are
                                almost certainly wrong.
                              type: string
                          type: object
                          x-kubernetes-map-type: atomic
                        sslEnabled:
                          description: sslEnabled Flag enable/disable SSL communication
                            with Gateway, default false
                          type: boolean
                        storageMode:
                          default: ThinProvisioned
                          description: |-
                            storageMode indicates whether the storage for a volume should be ThickProvisioned or ThinProvisioned.
                            Default is ThinProvisioned.
                          type: string
                        storagePool:
                          description: storagePool is the ScaleIO Storage Pool associated
                            with the protection domain.
                          type: string
                        system:
                          description: system is the name of the storage system as
                            configured in ScaleIO.
                          type: string
                        volumeName:
                          description: |-
                            volumeName is the name of a volume already created in the ScaleIO system
                            that is associated with this volume source.
                          type: string
                      required:
                      - gateway
                      - secretRef
                      - system
                      type: object
                    secret:
                      description: |-
                        secret represents a secret that should populate this volume.
                        More info: https://kubernetes.io/docs/concepts/storage/volumes#secret
                      properties:
                        defaultMode:
                          description: |-
                            defaultMode is Optional: mode bits used to set permissions on created files by default.
                            Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511.
                          format: int32
                          type: integer
                        items:
                          description: |-
                            items If unspecified, each key-value pair in the Data field of the referenced
                            Secret will be projected into the volume as a file whose name is the
                            key and content is the value.
                          items:
                            description: Maps a string key to a path within a volume.
                            properties:
                              key:
                                description: key is the key to project.
                                type: string
                              mode:
                                description: |-
                                  mode is Optional: mode bits used to set permissions on this file.
                                  Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511.
                                format: int32
                                type: integer
                              path:
                                description: |-
                                  path is the relative path of the file to map the key to.
                                  May not be an absolute path.
                                  May not contain the path element '..'.
                                  May not start with the string '..'.
                                type: string
                            required:
                            - key
                            - path
                            type: object
                          type: array
                          x-kubernetes-list-type: atomic
                        optional:
                          description: optional field specify whether the Secret or
                            its keys must be defined
                          type: boolean
                        secretName:
                          description: |-
                            secretName is the name of the secret in the pod's namespace to use.
                            More info: https://kubernetes.io/docs/concepts/storage/volumes#secret
                          type: string
                      type: object
                    storageos:
                      description: |-
                        storageOS represents a StorageOS volume attached and mounted on Kubernetes nodes.
                        Deprecated: StorageOS is deprecated and the in-tree storageos type is no longer supported.
                      properties:
                        fsType:
                          description: |-
                            fsType is the filesystem type to mount.
                            Must be a filesystem type supported by the host operating system.
                            Ex. "ext4", "xfs", "ntfs". Implicitly inferred to be "ext4" if unspecified.
                          type: string
                        readOnly:
                          description: |-
                            readOnly defaults to false (read/write). ReadOnly here will force
                            the ReadOnly setting in VolumeMounts.
                          type: boolean
                        secretRef:
                          description: |-
                            secretRef specifies the secret to use for obtaining the StorageOS API
                            credentials.  If not specified, default values will be attempted.
                          properties:
                            name:
                              default: ""
                              description: |-
                                Name of the referent.
                                This field is effectively required, but due to backwards compatibility is
                                allowed to be empty. Instances of this type with an empty value here are
                                almost certainly wrong.
                              type: string
                          type: object
                          x-kubernetes-map-type: atomic
                        volumeName:
                          description: |-
                            volumeName is the human-readable name of the StorageOS volume.  Volume
                            names are only unique within a namespace.
                          type: string
                        volumeNamespace:
                          description: |-
                            volumeNamespace specifies the scope of the volume within StorageOS.  If no
                            namespace is specified then the Pod's namespace will be used.
                          type: string
                      type: object
                    vsphereVolume:
                      description: |-
                        vsphereVolume represents a vSphere volume attached and mounted on kubelets host machine.
                        Deprecated: VsphereVolume is deprecated.
                      properties:
                        fsType:
                          description: |-
                            fsType is filesystem type to mount.
                            Must be a filesystem type supported by the host operating system.
                            Ex. "ext4", "xfs", "ntfs". Implicitly inferred to be "ext4" if unspecified.
                          type: string
                        storagePolicyID:
                          description: storagePolicyID is the storage Policy Based
                            Management (SPBM) profile ID associated with the StoragePolicyName.
                          type: string
                        storagePolicyName:
                          description: storagePolicyName is the storage Policy Based
                            Management (SPBM) profile name.
                          type: string
                        volumePath:
                          description: volumePath is the path that identifies vSphere
                            volume vmdk
                          type: string
                      required:
                      - volumePath
                      type: object
                  required:
                  - name
                  type: object
                type: array
            required:
            - updateStrategy
            type: object
          status:
            description: ClusterEngineRuntimeProfileStatus defines the observed state
              of ClusterEngineRuntimeProfile.
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
