name: 🐞 Bug report
description: Create a report to help us reproduce and fix the bug
title: "[Bug] "
labels: ['Bug']

body:
- type: checkboxes
  attributes:
    label: Checklist
    options:
    - label: 1. I have searched related issues but cannot get the expected help.
    - label: 2. The bug has not been fixed in the latest version.
    - label: 3. Please note that if the bug-related issue you submitted lacks corresponding environment info and a minimal reproducible demo, it will be challenging for us to reproduce and resolve the issue, reducing the likelihood of receiving feedback.
    - label: 4. If the issue you raised is not a bug but a question, please raise a discussion at https://github.com/sgl-project/sglang/discussions/new/choose Otherwise, it will be closed.
    - label: 5. Please use English, otherwise it will be closed.
- type: textarea
  attributes:
    label: Describe the bug
    description: A clear and concise description of what the bug is.
  validations:
    required: true
- type: textarea
  attributes:
    label: Reproduction
    description: |
      What command or script did you run? Which **model** are you using?
    placeholder: |
      A placeholder for the command.
  validations:
    required: true
- type: textarea
  attributes:
    label: Environment
    description: |
      Please provide necessary environment information here with `python3 -m sglang.check_env`. Otherwise the issue will be closed.
    placeholder: Environment here.
  validations:
    required: true
