apiVersion: v1
kind: Namespace
metadata:
  labels:
    control-plane: rbgs-controller
    app.kubernetes.io/name: rbgs
    app.kubernetes.io/managed-by: kustomize
  name: system
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rbgs-controller-manager
  namespace: system
  labels:
    control-plane: rbgs-controller
    app.kubernetes.io/name: rbgs
    app.kubernetes.io/managed-by: kustomize
spec:
  selector:
    matchLabels:
      control-plane: rbgs-controller
      app.kubernetes.io/name: rbgs
  replicas: 2
  template:
    metadata:
      labels:
        control-plane: rbgs-controller
        app.kubernetes.io/name: rbgs
    spec:
      serviceAccountName: rbgs-controller-sa
      containers:
        - name: rbgs
          args:
            - --metrics-bind-address=:8443
            - --leader-elect
            - --health-probe-bind-address=:8081
          command:
            - /manager
          image: controller:latest
          imagePullPolicy: IfNotPresent
          resources:
            limits:
              cpu: 1000m
              memory: 1536Mi
            requests:
              cpu: 100m
              memory: 256Mi