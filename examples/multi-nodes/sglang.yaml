apiVersion: leaderworkerset.x-k8s.io/v1
kind: LeaderWorkerSet
metadata:
  name: sglang-multi-nodes
spec:
  replicas: 1
  leaderWorkerTemplate:
    size: 2
    restartPolicy: RecreateGroupOnPodRestart
    leaderTemplate:
      metadata:
        labels:
          role: leader
          # for prometheus to scrape
          alibabacloud.com/inference-workload: sglang-multi-nodes
          alibabacloud.com/inference_backend: sglang
      spec:
        containers:
          - name: sglang-leader
            # lmsysorg/sglang:latest with mooncake
            image: anolis-registry.cn-zhangjiakou.cr.aliyuncs.com/anolis-docker-images/docker-temp:0.3.4.post2-sglang0.4.10.post2-pytorch2.7.1.8-cuda12.8.1-py312-alinux3.2104
            command:
              - sh
              - -c
              - "python3 -m sglang.launch_server --model-path /models/Qwen3-32B --tp 2 --dist-init-addr $(LWS_LEADER_ADDRESS):20000 \
              --nnodes $(LWS_GROUP_SIZE) --node-rank $(LWS_WORKER_INDEX) --trust-remote-code --host 0.0.0.0 --port 8000 --enable-metrics"
            resources:
              limits:
                nvidia.com/gpu: "1"
                memory: "48Gi"
                cpu: "12"
              requests:
                nvidia.com/gpu: "1"
                memory: "48Gi"
                cpu: "12"
            ports:
              - containerPort: 8000
                name: http
            readinessProbe:
              tcpSocket:
                port: 8000
              initialDelaySeconds: 30
              periodSeconds: 10
            volumeMounts:
              - mountPath: /dev/shm
                name: dshm
              - mountPath: /models/Qwen3-32B
                name: model
        volumes:
          - name: model
            persistentVolumeClaim:
              claimName: llm-model
          - name: dshm
            emptyDir:
              medium: Memory
              sizeLimit: 15Gi
    workerTemplate:
      spec:
        containers:
          - name: sglang-worker
            image: anolis-registry.cn-zhangjiakou.cr.aliyuncs.com/anolis-docker-images/docker-temp:0.3.4.post2-sglang0.4.10.post2-pytorch2.7.1.8-cuda12.8.1-py312-alinux3.2104
            command:
              - sh
              - -c
              - "python3 -m sglang.launch_server --model-path /models/Qwen3-32B --tp 2 --dist-init-addr $(LWS_LEADER_ADDRESS):20000 \
              --nnodes $(LWS_GROUP_SIZE) --node-rank $(LWS_WORKER_INDEX) --trust-remote-code"
            resources:
              limits:
                nvidia.com/gpu: "1"
                memory: "48Gi"
                cpu: "12"
              requests:
                nvidia.com/gpu: "1"
                memory: "48Gi"
                cpu: "12"
            volumeMounts:
              - mountPath: /dev/shm
                name: dshm
              - mountPath: /models/Qwen3-32B
                name: model
        volumes:
          - name: model
            persistentVolumeClaim:
              claimName: llm-model
          - name: dshm
            emptyDir:
              medium: Memory
              sizeLimit: 15Gi
---
apiVersion: v1
kind: Service
metadata:
  name: multi-nodes-service
spec:
  selector:
    alibabacloud.com/inference-workload: sglang-multi-nodes
    role: leader
  ports:
    - protocol: TCP
      port: 8000
      targetPort: 8000